# Generated by Django 3.1.3 on 2020-12-03 12:23

from django.apps.registry import Apps
from django.db import migrations
from django.db.backends.base.schema import BaseDatabaseSchemaEditor


def replace_pb_prefix(apps: Apps, schema_editor: BaseDatabaseSchemaEditor):
    ExpressionPolicy = apps.get_model("authentik_policies_expression", "ExpressionPolicy")

    db_alias = schema_editor.connection.alias

    for policy in ExpressionPolicy.objects.using(db_alias).all():
        # Because the previous migration had a broken replace, we have to replace here again
        policy.expression = policy.expression.replace("pb_flow_plan.", "context.")
        policy.expression = policy.expression.replace("pb_is_sso_flow", "ak_is_sso_flow")
        policy.save()


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies_expression", "0002_auto_20200926_1156"),
    ]

    operations = [
        migrations.RunPython(replace_pb_prefix),
    ]
