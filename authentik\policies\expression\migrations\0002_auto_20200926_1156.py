# Generated by Django 3.1.1 on 2020-09-26 11:56

from django.apps.registry import Apps
from django.db import migrations
from django.db.backends.base.schema import BaseDatabaseSchemaEditor


def remove_pb_flow_plan(apps: Apps, schema_editor: BaseDatabaseSchemaEditor):
    ExpressionPolicy = apps.get_model("authentik_policies_expression", "ExpressionPolicy")

    db_alias = schema_editor.connection.alias

    for policy in ExpressionPolicy.objects.using(db_alias).all():
        policy.expression = policy.expression.replace("pb_flow_plan.", "context.")
        policy.save()


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies_expression", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(remove_pb_flow_plan),
    ]
