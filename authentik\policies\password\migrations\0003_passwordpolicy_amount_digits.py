# Generated by Django 4.0 on 2021-12-18 14:54

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies_password", "0002_passwordpolicy_password_field"),
    ]

    operations = [
        migrations.AddField(
            model_name="passwordpolicy",
            name="amount_digits",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="passwordpolicy",
            name="amount_lowercase",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="passwordpolicy",
            name="amount_symbols",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="passwordpolicy",
            name="amount_uppercase",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="passwordpolicy",
            name="length_min",
            field=models.PositiveIntegerField(default=0),
        ),
    ]
