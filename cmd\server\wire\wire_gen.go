// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"fmt"
	"go-abm-idp/internal/config"
	"go-abm-idp/internal/handler"
	"go-abm-idp/internal/job"
	"go-abm-idp/internal/oauth2"
	"go-abm-idp/internal/repository"
	"go-abm-idp/internal/server"
	"go-abm-idp/internal/service"
	"go-abm-idp/pkg/app"
	"go-abm-idp/pkg/jwt"
	"go-abm-idp/pkg/log"
	"go-abm-idp/pkg/server/http"
	"go-abm-idp/pkg/sid"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	db := repository.NewDB(viperViper, logger)
	syncedEnforcer := repository.NewCasbinEnforcer(viperViper, logger, db)
	handlerHandler := handler.NewHandler(logger)
	repositoryRepository := repository.NewRepository(logger, db, syncedEnforcer)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	serviceService := service.NewService(transaction, logger, sidSid, jwtJWT)
	adminRepository := repository.NewAdminRepository(repositoryRepository)
	adminService := service.NewAdminService(serviceService, adminRepository)
	adminHandler := handler.NewAdminHandler(handlerHandler, adminService)
	userRepository := repository.NewUserRepository(repositoryRepository)
	userService := service.NewUserService(serviceService, userRepository)
	userHandler := handler.NewUserHandler(handlerHandler, userService)
	loginHandler := handler.NewLoginHandler(handlerHandler)
	oAuth2Repository := repository.NewOAuth2Repository(repositoryRepository)
	oAuth2AdminService := service.NewOAuth2AdminService(repositoryRepository, oAuth2Repository, logger)
	oAuth2AdminHandler := handler.NewOAuth2AdminHandler(handlerHandler, oAuth2AdminService)
	config := provideConfig(viperViper)
	jwtManager, err := provideJWTManager(viperViper)
	if err != nil {
		return nil, nil, err
	}
	oidcService := service.NewOIDCService(db, config, jwtManager, userRepository, oAuth2Repository)
	oAuth2StandardHandler := handler.NewOAuth2StandardHandler(handlerHandler, oAuth2AdminService, oidcService)
	oAuth2ConsentHandler := handler.NewOAuth2ConsentHandler(handlerHandler, oAuth2AdminService, oidcService)
	ssfService := service.NewSSFService(db, config)
	ssfjwtService := service.NewSSFJWTService(db, config)
	ssfFixedHandler := handler.NewSSFFixedHandler(ssfService, ssfjwtService, config)
	ssfStandardHandler := handler.NewSSFStandardHandler(handlerHandler, ssfService)
	zapLogger := provideZapLogger(logger)
	oAuth2AuditLogger := service.NewOAuth2AuditLogger(db, zapLogger)
	ssfEventManager := service.NewSSFEventManager(db, zapLogger, oAuth2AuditLogger)
	ssfProviderService := service.NewSSFProviderService(db, ssfEventManager, zapLogger)
	ssfHandler := handler.NewSSFHandler(handlerHandler, ssfProviderService, ssfEventManager, zapLogger)
	oAuth2ProductionService := service.NewOAuth2ProductionService(db, logger, config)
	oAuth2ProductionHandler := handler.NewOAuth2ProductionHandler(handlerHandler, oAuth2ProductionService, oidcService, zapLogger)
	appleBusinessManagerHandler, err := provideAppleBusinessManagerHandler(logger)
	if err != nil {
		return nil, nil, err
	}
	eventService := service.NewEventService(zapLogger)
	appleUserSyncService := service.NewAppleUserSyncService(zapLogger, eventService)
	httpServer := server.NewHTTPServer(logger, viperViper, jwtJWT, syncedEnforcer, adminHandler, userHandler, loginHandler, oAuth2AdminHandler, oAuth2StandardHandler, oAuth2ConsentHandler, ssfFixedHandler, ssfStandardHandler, ssfHandler, oAuth2ProductionHandler, appleBusinessManagerHandler, appleUserSyncService)
	jobJob := job.NewJob(transaction, logger, sidSid)
	userJob := job.NewUserJob(jobJob, userRepository)
	jobServer := server.NewJobServer(logger, userJob)
	appApp := newApp(httpServer, jobServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRepository, repository.NewTransaction, repository.NewUserRepository, repository.NewCasbinEnforcer, repository.NewAdminRepository, repository.NewOAuth2Repository)

var serviceSet = wire.NewSet(service.NewService, service.NewUserService, service.NewAdminService, service.NewOAuth2AdminService, service.NewOIDCService, service.NewSSFService, service.NewSSFJWTService, service.NewOAuth2AuditLogger, service.NewSSFEventManager, service.NewSSFProviderService, service.NewOAuth2AdapterService, service.NewOAuth2IntrospectionAdapter, service.NewOAuth2RevocationAdapter, service.NewOAuth2ProductionService)

var handlerSet = wire.NewSet(handler.NewHandler, handler.NewUserHandler, handler.NewAdminHandler, handler.NewLoginHandler, handler.NewOAuth2AdminHandler, handler.NewOAuth2StandardHandler, handler.NewOAuth2ConsentHandler, handler.NewOAuth2ProductionHandler, handler.NewSSFFixedHandler, handler.NewSSFStandardHandler, handler.NewSSFHandler)

var jobSet = wire.NewSet(job.NewJob, job.NewUserJob)

var serverSet = wire.NewSet(server.NewHTTPServer, server.NewJobServer)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, jobServer), app.WithName("demo-server"))
}

// provideJWTManager 提供JWT管理
func provideJWTManager(v *viper.Viper) (*oauth2.JWTManager, error) {

	issuer := v.GetString("jwt.issuer")
	if issuer == "" {
		issuer = "nunu-oauth2"
	}
	keyID := v.GetString("jwt.key_id")
	if keyID == "" {
		keyID = "default"
	}

	rsaPrivateKeyPEM := v.GetString("jwt.rsa_private_key")
	if rsaPrivateKeyPEM != "" {

		return oauth2.NewJWTManagerFromPEM(rsaPrivateKeyPEM, issuer, keyID)
	}

	rsaKey, err := oauth2.GenerateRSAKey(2048)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %w", err)
	}

	return oauth2.NewJWTManager(rsaKey, issuer, keyID)
}

// provideConfig 提供配置
func provideConfig(v *viper.Viper) *config.Config {
	// 🔧 Apple Business Manager修复：从viper配置中读取实际配置
	cfg := config.DefaultConfig()

	// 从viper中读取服务器配置
	if v.IsSet("server.base_url") {
		cfg.Server.BaseURL = v.GetString("server.base_url")
	}
	if v.IsSet("server.host") {
		cfg.Server.Host = v.GetString("server.host")
	}
	if v.IsSet("server.port") {
		cfg.Server.Port = v.GetInt("server.port")
	}
	if v.IsSet("server.mode") {
		cfg.Server.Mode = v.GetString("server.mode")
	}

	// 从viper中读取JWT配置
	if v.IsSet("jwt.issuer") {
		cfg.JWT.Issuer = v.GetString("jwt.issuer")
	}
	if v.IsSet("jwt.key_id") {
		cfg.JWT.KeyID = v.GetString("jwt.key_id")
	}

	return cfg
}

// provideZapLogger 提供zap.Logger
func provideZapLogger(logger *log.Logger) *zap.Logger {

	return logger.Logger
}

// provideAppleBusinessManagerHandler 提供Apple Business Manager处理器
func provideAppleBusinessManagerHandler(logger *log.Logger) (*handler.AppleBusinessManagerHandler, error) {
	return handler.NewAppleBusinessManagerHandler(logger)
}
