# Generated by Django 4.1.7 on 2023-03-07 13:41

from django.db import migrations, models

from authentik.lib.migrations import fallback_names


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_core", "0025_alter_provider_authorization_flow"),
    ]

    operations = [
        migrations.RunPython(fallback_names("authentik_core", "propertymapping", "name")),
        migrations.RunPython(fallback_names("authentik_core", "provider", "name")),
        migrations.AlterField(
            model_name="propertymapping",
            name="name",
            field=models.TextField(unique=True),
        ),
        migrations.AlterField(
            model_name="provider",
            name="name",
            field=models.TextField(unique=True),
        ),
    ]
