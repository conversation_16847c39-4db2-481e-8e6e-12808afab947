# Generated by Django 5.1.9 on 2025-05-19 18:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentik_crypto", "0004_alter_certificatekeypair_name"),
        ("authentik_flows", "0027_auto_20231028_1424"),
    ]

    operations = [
        migrations.CreateModel(
            name="MutualTLSStage",
            fields=[
                (
                    "stage_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_flows.stage",
                    ),
                ),
                (
                    "mode",
                    models.TextField(choices=[("optional", "Optional"), ("required", "Required")]),
                ),
                (
                    "cert_attribute",
                    models.TextField(
                        choices=[
                            ("subject", "Subject"),
                            ("common_name", "Common Name"),
                            ("email", "Email"),
                        ]
                    ),
                ),
                (
                    "user_attribute",
                    models.TextField(choices=[("username", "Username"), ("email", "Email")]),
                ),
                (
                    "certificate_authorities",
                    models.ManyToManyField(
                        blank=True,
                        default=None,
                        help_text="Configure certificate authorities to validate the certificate against. This option has a higher priority than the `client_certificate` option on `Brand`.",
                        to="authentik_crypto.certificatekeypair",
                    ),
                ),
            ],
            options={
                "verbose_name": "Mutual TLS Stage",
                "verbose_name_plural": "Mutual TLS Stages",
                "permissions": [
                    ("pass_outpost_certificate", "Permissions to pass Certificates for outposts.")
                ],
            },
            bases=("authentik_flows.stage",),
        ),
    ]
