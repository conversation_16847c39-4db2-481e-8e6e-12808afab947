# Generated by Django 5.0.6 on 2024-05-09 12:57

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    replaces = [
        ("authentik_providers_google_workspace", "0001_initial"),
        (
            "authentik_providers_google_workspace",
            "0002_alter_googleworkspaceprovidergroup_options_and_more",
        ),
    ]

    initial = True

    dependencies = [
        ("authentik_core", "0035_alter_group_options_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="GoogleWorkspaceProviderMapping",
            fields=[
                (
                    "propertymapping_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_core.propertymapping",
                    ),
                ),
            ],
            options={
                "verbose_name": "Google Workspace Provider Mapping",
                "verbose_name_plural": "Google Workspace Provider Mappings",
            },
            bases=("authentik_core.propertymapping",),
        ),
        migrations.CreateModel(
            name="GoogleWorkspaceProvider",
            fields=[
                (
                    "provider_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_core.provider",
                    ),
                ),
                ("delegated_subject", models.EmailField(max_length=254)),
                ("credentials", models.JSONField()),
                (
                    "scopes",
                    models.TextField(
                        default="https://www.googleapis.com/auth/admin.directory.user,https://www.googleapis.com/auth/admin.directory.group,https://www.googleapis.com/auth/admin.directory.group.member,https://www.googleapis.com/auth/admin.directory.domain.readonly"
                    ),
                ),
                ("default_group_email_domain", models.TextField()),
                ("exclude_users_service_account", models.BooleanField(default=False)),
                (
                    "user_delete_action",
                    models.TextField(
                        choices=[
                            ("do_nothing", "Do Nothing"),
                            ("delete", "Delete"),
                            ("suspend", "Suspend"),
                        ],
                        default="delete",
                    ),
                ),
                (
                    "group_delete_action",
                    models.TextField(
                        choices=[
                            ("do_nothing", "Do Nothing"),
                            ("delete", "Delete"),
                            ("suspend", "Suspend"),
                        ],
                        default="delete",
                    ),
                ),
                (
                    "filter_group",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_DEFAULT,
                        to="authentik_core.group",
                    ),
                ),
                (
                    "property_mappings_group",
                    models.ManyToManyField(
                        blank=True,
                        default=None,
                        help_text="Property mappings used for group creation/updating.",
                        to="authentik_core.propertymapping",
                    ),
                ),
            ],
            options={
                "verbose_name": "Google Workspace Provider",
                "verbose_name_plural": "Google Workspace Providers",
            },
            bases=("authentik_core.provider", models.Model),
        ),
        migrations.CreateModel(
            name="GoogleWorkspaceProviderGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ("google_id", models.TextField()),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="authentik_core.group"
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_providers_google_workspace.googleworkspaceprovider",
                    ),
                ),
            ],
            options={
                "unique_together": {("google_id", "group", "provider")},
                "verbose_name": "Google Workspace Provider Group",
                "verbose_name_plural": "Google Workspace Provider Groups",
            },
        ),
        migrations.CreateModel(
            name="GoogleWorkspaceProviderUser",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ("google_id", models.TextField()),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_providers_google_workspace.googleworkspaceprovider",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                "unique_together": {("google_id", "user", "provider")},
                "verbose_name": "Google Workspace Provider User",
                "verbose_name_plural": "Google Workspace Provider Users",
            },
        ),
    ]
