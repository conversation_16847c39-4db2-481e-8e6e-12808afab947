# Generated by Django 5.0.10 on 2025-01-27 20:05

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models

import authentik.lib.utils.time


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentik_flows", "0027_auto_20231028_1424"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AuthenticatorEmailStage",
            fields=[
                (
                    "stage_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_flows.stage",
                    ),
                ),
                ("friendly_name", models.TextField(null=True)),
                (
                    "use_global_settings",
                    models.BooleanField(
                        default=False,
                        help_text="When enabled, global Email connection settings will be used and connection settings below will be ignored.",
                    ),
                ),
                ("host", models.TextField(default="localhost")),
                ("port", models.IntegerField(default=25)),
                ("username", models.TextField(blank=True, default="")),
                ("password", models.TextField(blank=True, default="")),
                ("use_tls", models.BooleanField(default=False)),
                ("use_ssl", models.BooleanField(default=False)),
                ("timeout", models.IntegerField(default=10)),
                (
                    "from_address",
                    models.EmailField(default="<EMAIL>", max_length=254),
                ),
                (
                    "token_expiry",
                    models.TextField(
                        default="minutes=30",
                        help_text="Time the token sent is valid (Format: hours=3,minutes=17,seconds=300).",
                        validators=[authentik.lib.utils.time.timedelta_string_validator],
                    ),
                ),
                ("subject", models.TextField(default="authentik Sign-in code")),
                ("template", models.TextField(default="email/email_otp.html")),
                (
                    "configure_flow",
                    models.ForeignKey(
                        blank=True,
                        help_text="Flow used by an authenticated user to configure this Stage. If empty, user will not be able to configure this stage.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="authentik_flows.flow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Authenticator Setup Stage",
                "verbose_name_plural": "Email Authenticator Setup Stages",
            },
            bases=("authentik_flows.stage", models.Model),
        ),
        migrations.CreateModel(
            name="EmailDevice",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "name",
                    models.CharField(
                        help_text="The human-readable name of this device.", max_length=64
                    ),
                ),
                (
                    "confirmed",
                    models.BooleanField(default=True, help_text="Is this device ready for use?"),
                ),
                ("token", models.CharField(blank=True, max_length=16, null=True)),
                (
                    "valid_until",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="The timestamp of the moment of expiry of the saved token.",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("last_used", models.DateTimeField(auto_now=True)),
                (
                    "stage",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_stages_authenticator_email.authenticatoremailstage",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Device",
                "verbose_name_plural": "Email Devices",
                "unique_together": {("user", "email")},
            },
        ),
    ]
