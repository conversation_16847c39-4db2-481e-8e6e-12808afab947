# Apple Business Manager OIDC端点完整验证脚本
# 基于Authentik分析结果的端点测试

param(
    [string]$BaseURL = "http://localhost:8000",
    [switch]$Verbose
)

Write-Host "🔍 Apple Business Manager OIDC端点验证开始..." -ForegroundColor Green
Write-Host "基础URL: $BaseURL" -ForegroundColor Cyan

# 测试结果统计
$TestResults = @{
    Passed = 0
    Failed = 0
    Total = 0
}

# 测试函数
function Test-Endpoint {
    param(
        [string]$Name,
        [string]$URL,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null,
        [string]$ExpectedContentType = "application/json",
        [int]$ExpectedStatusCode = 200,
        [string[]]$RequiredFields = @()
    )
    
    $TestResults.Total++
    Write-Host "`n📋 测试: $Name" -ForegroundColor Yellow
    Write-Host "   URL: $URL" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $URL
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        $response = Invoke-WebRequest @params
        
        # 检查状态码
        if ($response.StatusCode -ne $ExpectedStatusCode) {
            Write-Host "   ❌ 状态码错误: 期望 $ExpectedStatusCode, 实际 $($response.StatusCode)" -ForegroundColor Red
            $TestResults.Failed++
            return $false
        }
        
        # 检查Content-Type
        $contentType = $response.Headers["Content-Type"]
        if ($contentType -and -not $contentType.StartsWith($ExpectedContentType)) {
            Write-Host "   ⚠️  Content-Type警告: 期望 $ExpectedContentType, 实际 $contentType" -ForegroundColor Yellow
        }
        
        # 解析JSON响应
        if ($ExpectedContentType -eq "application/json") {
            try {
                $jsonResponse = $response.Content | ConvertFrom-Json
                
                # 检查必需字段
                foreach ($field in $RequiredFields) {
                    if (-not $jsonResponse.PSObject.Properties.Name.Contains($field)) {
                        Write-Host "   ❌ 缺少必需字段: $field" -ForegroundColor Red
                        $TestResults.Failed++
                        return $false
                    }
                }
                
                if ($Verbose) {
                    Write-Host "   📄 响应内容:" -ForegroundColor Gray
                    $jsonResponse | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor Gray
                }
                
            } catch {
                Write-Host "   ❌ JSON解析失败: $($_.Exception.Message)" -ForegroundColor Red
                $TestResults.Failed++
                return $false
            }
        }
        
        Write-Host "   ✅ 测试通过" -ForegroundColor Green
        $TestResults.Passed++
        return $true
        
    } catch {
        Write-Host "   ❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
        $TestResults.Failed++
        return $false
    }
}

# 1. 测试OIDC配置发现端点 (高优先级)
Write-Host "`n🔧 高优先级端点测试" -ForegroundColor Magenta

$oidcRequiredFields = @(
    "issuer",
    "authorization_endpoint", 
    "token_endpoint",
    "userinfo_endpoint",
    "jwks_uri",
    "scopes_supported",
    "response_types_supported",
    "grant_types_supported",
    "subject_types_supported",
    "id_token_signing_alg_values_supported",
    "token_endpoint_auth_methods_supported",
    "code_challenge_methods_supported",
    "claims_supported"
)

Test-Endpoint -Name "OIDC配置发现" -URL "$BaseURL/.well-known/openid_configuration" -RequiredFields $oidcRequiredFields

# 2. 测试JWKS端点 (高优先级)
$jwksRequiredFields = @("keys")

Test-Endpoint -Name "JWKS端点" -URL "$BaseURL/oauth2/jwks" -RequiredFields $jwksRequiredFields

# 3. 测试SSF配置端点 (中优先级)
Write-Host "`n🔧 中优先级端点测试" -ForegroundColor Magenta

$ssfRequiredFields = @(
    "issuer",
    "jwks_uri",
    "delivery_methods_supported",
    "events_supported",
    "critical_subject_members"
)

Test-Endpoint -Name "SSF配置发现" -URL "$BaseURL/.well-known/ssf_configuration" -RequiredFields $ssfRequiredFields

# 4. 测试OAuth2授权端点 (需要参数)
Write-Host "`n🔧 OAuth2端点基础测试" -ForegroundColor Magenta

# 测试授权端点 (应该返回400因为缺少参数)
Test-Endpoint -Name "OAuth2授权端点 (无参数)" -URL "$BaseURL/oauth2/authorize" -ExpectedStatusCode 400

# 5. 测试令牌端点 (应该返回400因为缺少参数)
Test-Endpoint -Name "OAuth2令牌端点 (无参数)" -URL "$BaseURL/oauth2/token" -Method "POST" -ExpectedStatusCode 400

# 6. 测试用户信息端点 (应该返回401因为缺少认证)
Test-Endpoint -Name "OAuth2用户信息端点 (无认证)" -URL "$BaseURL/oauth2/userinfo" -ExpectedStatusCode 401

# 7. 测试兼容性重定向端点
Write-Host "`n🔧 兼容性端点测试" -ForegroundColor Magenta

# 测试Authentik风格的重定向
Test-Endpoint -Name "应用JWKS重定向" -URL "$BaseURL/application/o/default/jwks/" -ExpectedStatusCode 301

# 8. 测试CORS支持
Write-Host "`n🔧 CORS支持测试" -ForegroundColor Magenta

$corsHeaders = @{
    "Origin" = "https://business.apple.com"
    "Access-Control-Request-Method" = "GET"
    "Access-Control-Request-Headers" = "Authorization"
}

Test-Endpoint -Name "CORS预检请求" -URL "$BaseURL/oauth2/jwks" -Method "OPTIONS" -Headers $corsHeaders -ExpectedStatusCode 200

# 9. 测试Apple Business Manager特定端点
Write-Host "`n🔧 Apple Business Manager特定测试" -ForegroundColor Magenta

# 测试Apple风格的JWKS端点
Test-Endpoint -Name "Apple JWKS端点" -URL "$BaseURL/jwks" -RequiredFields @("keys")

# 10. 性能和缓存测试
Write-Host "`n🔧 性能和缓存测试" -ForegroundColor Magenta

# 测试缓存头
$start = Get-Date
Test-Endpoint -Name "OIDC配置缓存测试" -URL "$BaseURL/.well-known/openid_configuration"
$end = Get-Date
$duration = ($end - $start).TotalMilliseconds

Write-Host "   ⏱️  响应时间: $([math]::Round($duration, 2))ms" -ForegroundColor Cyan

# 11. 安全头测试
Write-Host "`n🔧 安全头测试" -ForegroundColor Magenta

try {
    $response = Invoke-WebRequest -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
    
    $securityHeaders = @(
        "Cache-Control",
        "X-Content-Type-Options",
        "X-Frame-Options"
    )
    
    foreach ($header in $securityHeaders) {
        if ($response.Headers.ContainsKey($header)) {
            Write-Host "   ✅ 安全头存在: $header = $($response.Headers[$header])" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  安全头缺失: $header" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "   ❌ 安全头测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 12. Apple Business Manager错误码-27480相关测试
Write-Host "`n🔧 Apple Business Manager错误码-27480相关测试" -ForegroundColor Magenta

# 测试OIDC配置的issuer字段格式
try {
    $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration"
    
    if ($oidcConfig.issuer) {
        Write-Host "   ✅ Issuer字段存在: $($oidcConfig.issuer)" -ForegroundColor Green
        
        # 检查issuer格式是否符合Apple Business Manager要求
        if ($oidcConfig.issuer -match "^https?://.*") {
            Write-Host "   ✅ Issuer格式正确 (HTTP/HTTPS URL)" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Issuer格式错误: 应该是完整的HTTP/HTTPS URL" -ForegroundColor Red
        }
        
        # 检查端点URL一致性
        $baseIssuer = $oidcConfig.issuer -replace "/application/o/abm$", ""
        if ($oidcConfig.jwks_uri.StartsWith($baseIssuer)) {
            Write-Host "   ✅ JWKS URI与issuer基础URL一致" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  JWKS URI与issuer基础URL不一致" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ❌ Issuer字段缺失" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Issuer测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 输出测试结果统计
Write-Host "`n📊 测试结果统计" -ForegroundColor Magenta
Write-Host "总测试数: $($TestResults.Total)" -ForegroundColor Cyan
Write-Host "通过: $($TestResults.Passed)" -ForegroundColor Green
Write-Host "失败: $($TestResults.Failed)" -ForegroundColor Red

$successRate = if ($TestResults.Total -gt 0) { 
    [math]::Round(($TestResults.Passed / $TestResults.Total) * 100, 2) 
} else { 0 }

Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 70) { "Yellow" } else { "Red" })

# 最终结论
Write-Host "`n🎯 最终结论" -ForegroundColor Magenta

if ($TestResults.Failed -eq 0) {
    Write-Host "🎉 所有测试通过！Apple Business Manager集成准备就绪。" -ForegroundColor Green
} elseif ($TestResults.Failed -le 2) {
    Write-Host "⚠️  大部分测试通过，有少量问题需要修复。" -ForegroundColor Yellow
} else {
    Write-Host "❌ 多个测试失败，需要进一步修复。" -ForegroundColor Red
}

Write-Host "`n基于Authentik分析的修复建议:" -ForegroundColor Cyan
Write-Host "1. 确保所有端点返回正确的Content-Type" -ForegroundColor Gray
Write-Host "2. 验证OIDC配置包含所有必需字段" -ForegroundColor Gray
Write-Host "3. 检查JWKS格式符合RFC 7517规范" -ForegroundColor Gray
Write-Host "4. 确保PKCE支持完整实现" -ForegroundColor Gray
Write-Host "5. 验证客户端认证方法支持" -ForegroundColor Gray

Write-Host "`n🔍 Apple Business Manager OIDC端点验证完成!" -ForegroundColor Green
