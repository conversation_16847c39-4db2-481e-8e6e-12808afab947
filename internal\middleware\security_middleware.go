package middleware

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"go-abm-idp/internal/service"
)

// SecurityMiddleware 基于Authentik模式的企业级安全中间件
type SecurityMiddleware struct {
	logger       *zap.Logger
	eventService *service.EventService
	rateLimiter  *RateLimiter
}

// NewSecurityMiddleware 创建安全中间件
func NewSecurityMiddleware(logger *zap.Logger, eventService *service.EventService) *SecurityMiddleware {
	return &SecurityMiddleware{
		logger:       logger,
		eventService: eventService,
		rateLimiter:  NewRateLimiter(100, time.Minute), // 使用现有的RateLimiter
	}
}

// SecurityHeaders 设置安全响应头中间件
// 基于Authentik的安全最佳实践
func (sm *SecurityMiddleware) SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 基础安全头
		c.Header("X-Content-Type-Options", "nosniff")
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")
		c.<PERSON><PERSON>("X-XSS-Protection", "1; mode=block")
		c.<PERSON><PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")

		// OAuth2特定的缓存控制
		if strings.HasPrefix(c.Request.URL.Path, "/oauth2/") ||
			strings.HasPrefix(c.Request.URL.Path, "/.well-known/") {
			c.Header("Cache-Control", "no-store, no-cache, must-revalidate")
			c.Header("Pragma", "no-cache")
		}

		// CORS头 (针对Apple Business Manager)
		origin := c.GetHeader("Origin")
		if sm.isAllowedOrigin(origin) {
			c.Header("Access-Control-Allow-Origin", origin)
			c.Header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
			c.Header("Access-Control-Allow-Headers", "Authorization, Content-Type")
			c.Header("Access-Control-Max-Age", "86400")
		}

		// 处理CORS预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}

// RequestLogging 请求日志中间件
// 基于Authentik的请求记录模式
func (sm *SecurityMiddleware) RequestLogging() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")

		// 记录请求开始
		sm.logger.Debug("Request started",
			zap.String("method", method),
			zap.String("path", path),
			zap.String("client_ip", clientIP),
			zap.String("user_agent", userAgent))

		c.Next()

		// 记录请求完成
		duration := time.Since(start)
		statusCode := c.Writer.Status()

		// 根据状态码选择日志级别
		logLevel := zap.InfoLevel
		if statusCode >= 400 && statusCode < 500 {
			logLevel = zap.WarnLevel
		} else if statusCode >= 500 {
			logLevel = zap.ErrorLevel
		}

		sm.logger.Log(logLevel, "Request completed",
			zap.String("method", method),
			zap.String("path", path),
			zap.String("client_ip", clientIP),
			zap.Int("status_code", statusCode),
			zap.Duration("duration", duration),
			zap.Int("response_size", c.Writer.Size()))

		// 记录安全相关事件
		if statusCode == 401 || statusCode == 403 {
			context := service.NewEventContext().
				SetRequest(c).
				Set("status_code", statusCode).
				Set("duration_ms", duration.Milliseconds())

			sm.eventService.RecordEventWithRequest(c,
				service.EventSuspiciousRequest,
				fmt.Sprintf("Unauthorized access attempt: %s %s", method, path),
				context)
		}
	}
}

// RateLimit 速率限制中间件
// 基于Authentik的速率限制策略
func (sm *SecurityMiddleware) RateLimit(maxRequests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		if !sm.rateLimiter.Allow(clientIP) {
			// 记录速率限制事件
			sm.eventService.RecordSecurityEvent(c,
				service.EventRateLimitExceeded,
				fmt.Sprintf("Rate limit exceeded for IP %s", clientIP),
				service.EventLevelWarning)

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":             "rate_limit_exceeded",
				"error_description": "Too many requests",
				"retry_after":       int(window.Seconds()),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// SuspiciousActivityDetection 可疑活动检测中间件
// 基于Authentik的异常检测模式
func (sm *SecurityMiddleware) SuspiciousActivityDetection() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")
		path := c.Request.URL.Path

		// 检测可疑的User-Agent
		if sm.isSuspiciousUserAgent(userAgent) {
			sm.eventService.RecordSecurityEvent(c,
				service.EventSuspiciousRequest,
				fmt.Sprintf("Suspicious User-Agent detected: %s", userAgent),
				service.EventLevelWarning)
		}

		// 检测可疑的请求模式
		if sm.isSuspiciousPath(path) {
			sm.eventService.RecordSecurityEvent(c,
				service.EventSuspiciousRequest,
				fmt.Sprintf("Suspicious path access: %s", path),
				service.EventLevelWarning)
		}

		// 检测可疑的IP地址
		if sm.isSuspiciousIP(clientIP) {
			sm.eventService.RecordSecurityEvent(c,
				service.EventSuspiciousRequest,
				fmt.Sprintf("Request from suspicious IP: %s", clientIP),
				service.EventLevelError)
		}

		c.Next()
	}
}

// OAuth2EventLogging OAuth2特定事件记录中间件
func (sm *SecurityMiddleware) OAuth2EventLogging() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// 只处理OAuth2相关路径
		if !strings.HasPrefix(path, "/oauth2/") {
			c.Next()
			return
		}

		c.Next()

		// 根据路径和状态码记录事件
		statusCode := c.Writer.Status()
		clientID := sm.extractClientID(c)

		switch {
		case strings.Contains(path, "/authorize"):
			action := service.EventAuthorizeApplication
			if statusCode >= 400 {
				action = service.EventLoginFailed
			}
			sm.eventService.RecordOAuth2Event(c, action, clientID, "authorization_code", statusCode < 400)

		case strings.Contains(path, "/token"):
			grantType := c.PostForm("grant_type")
			action := service.EventTokenIssued
			if grantType == "refresh_token" {
				action = service.EventTokenRefreshed
			}
			if statusCode >= 400 {
				action = service.EventLoginFailed
			}
			sm.eventService.RecordOAuth2Event(c, action, clientID, grantType, statusCode < 400)

		case strings.Contains(path, "/introspect"):
			sm.eventService.RecordOAuth2Event(c, service.EventTokenIntrospected, clientID, "", statusCode < 400)

		case strings.Contains(path, "/revoke"):
			sm.eventService.RecordOAuth2Event(c, service.EventTokenRevoked, clientID, "", statusCode < 400)
		}
	}
}

// 辅助方法

// isAllowedOrigin 检查是否为允许的源
func (sm *SecurityMiddleware) isAllowedOrigin(origin string) bool {
	allowedOrigins := []string{
		"https://business.apple.com",
		"https://school.apple.com",
		"https://deploy.apple.com",
		"https://appleid.apple.com",
	}

	for _, allowed := range allowedOrigins {
		if origin == allowed {
			return true
		}
	}

	// 开发环境允许localhost
	if strings.Contains(origin, "localhost") || strings.Contains(origin, "127.0.0.1") {
		return true
	}

	return false
}

// isSuspiciousUserAgent 检查是否为可疑的User-Agent
func (sm *SecurityMiddleware) isSuspiciousUserAgent(userAgent string) bool {
	suspiciousPatterns := []string{
		"sqlmap",
		"nikto",
		"nmap",
		"masscan",
		"python-requests", // 可能的脚本攻击
		"curl/",           // 简单的curl请求可能是探测
	}

	userAgentLower := strings.ToLower(userAgent)
	for _, pattern := range suspiciousPatterns {
		if strings.Contains(userAgentLower, pattern) {
			return true
		}
	}

	return false
}

// isSuspiciousPath 检查是否为可疑的路径
func (sm *SecurityMiddleware) isSuspiciousPath(path string) bool {
	suspiciousPaths := []string{
		"/.env",
		"/admin",
		"/wp-admin",
		"/phpmyadmin",
		"/.git",
		"/config",
		"/backup",
	}

	pathLower := strings.ToLower(path)
	for _, suspicious := range suspiciousPaths {
		if strings.Contains(pathLower, suspicious) {
			return true
		}
	}

	return false
}

// isSuspiciousIP 检查是否为可疑的IP地址
func (sm *SecurityMiddleware) isSuspiciousIP(ip string) bool {
	// TODO: 实现IP黑名单检查
	// 这里可以集成第三方威胁情报服务

	// 简单的本地黑名单示例
	blacklistedIPs := []string{
		"0.0.0.0",
		"127.0.0.1", // 排除本地测试
	}

	for _, blacklisted := range blacklistedIPs {
		if ip == blacklisted && ip != "127.0.0.1" { // 允许本地测试
			return true
		}
	}

	return false
}

// extractClientID 从请求中提取客户端ID
func (sm *SecurityMiddleware) extractClientID(c *gin.Context) string {
	// 从查询参数获取
	if clientID := c.Query("client_id"); clientID != "" {
		return clientID
	}

	// 从POST表单获取
	if clientID := c.PostForm("client_id"); clientID != "" {
		return clientID
	}

	return "unknown"
}
