# Generated by Django 5.0.12 on 2025-02-24 19:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentik_providers_google_workspace",
            "0003_googleworkspaceprovidergroup_attributes_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="googleworkspaceprovider",
            name="dry_run",
            field=models.BooleanField(
                default=False,
                help_text="When enabled, provider will not modify or create objects in the remote system.",
            ),
        ),
    ]
