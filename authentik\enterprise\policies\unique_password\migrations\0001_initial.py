# Generated by Django 5.0.13 on 2025-03-26 23:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentik_policies", "0011_policybinding_failure_result_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UniquePasswordPolicy",
            fields=[
                (
                    "policy_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_policies.policy",
                    ),
                ),
                (
                    "password_field",
                    models.TextField(
                        default="password",
                        help_text="Field key to check, field keys defined in Prompt stages are available.",
                    ),
                ),
                (
                    "num_historical_passwords",
                    models.PositiveIntegerField(
                        default=1, help_text="Number of passwords to check against."
                    ),
                ),
            ],
            options={
                "verbose_name": "Password Uniqueness Policy",
                "verbose_name_plural": "Password Uniqueness Policies",
                "indexes": [
                    models.Index(fields=["policy_ptr_id"], name="authentik_p_policy__f559aa_idx")
                ],
            },
            bases=("authentik_policies.policy",),
        ),
        migrations.CreateModel(
            name="UserPasswordHistory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("old_password", models.CharField(max_length=128)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("hibp_prefix_sha1", models.CharField(max_length=5)),
                ("hibp_pw_hash", models.TextField()),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="old_passwords",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Password History",
            },
        ),
    ]
