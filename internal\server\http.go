package server

import (
	"fmt"
	nethttp "net/http"
	"strings"
	"time"

	// "go-abm-idp/docs"  // 暂时注释掉,等swagger文档生成后再启用
	"go-abm-idp/internal/handler"
	"go-abm-idp/internal/middleware"
	"go-abm-idp/internal/service"
	"go-abm-idp/pkg/jwt"
	"go-abm-idp/pkg/log"
	"go-abm-idp/pkg/server/http"
	"go-abm-idp/web"

	"github.com/casbin/casbin/v2"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func NewHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	jwt *jwt.JWT,
	e *casbin.SyncedEnforcer,
	adminHandler *handler.Ad<PERSON><PERSON>andler,
	userHandler *handler.User<PERSON><PERSON><PERSON>,
	loginHandler *handler.<PERSON><PERSON><PERSON><PERSON><PERSON>,
	oauth2AdminHandler *handler.O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	oauth2StandardHandler *handler.OAuth2StandardHandler,
	oauth2ConsentHandler *handler.OAuth2ConsentHandler,
	ssfHandler *handler.SSFFixedHandler,
	ssfStandardHandler *handler.SSFStandardHandler,
	// 新增的高级处理器
	ssfAdvancedHandler *handler.SSFHandler,
	oauth2ProductionHandler *handler.OAuth2ProductionHandler,
	// Apple Business Manager处理器
	appleHandler *handler.AppleBusinessManagerHandler,
) *http.Server {
	// 设置Gin模式
	if conf.GetString("app.env") == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 创建Gin引擎,不使用默认中间件
	engine := gin.New()

	// 加载HTML模板
	engine.LoadHTMLGlob("web/templates/*")

	// 创建企业级服务
	eventService := service.NewEventService(zap.L())
	securityMiddleware := middleware.NewSecurityMiddleware(zap.L(), eventService)

	// 手动添加必要的中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())

	// 添加企业级安全中间件 (基于Authentik模式)
	engine.Use(securityMiddleware.SecurityHeaders())
	engine.Use(securityMiddleware.RequestLogging())
	engine.Use(securityMiddleware.RateLimit(100, time.Minute)) // 每分钟100请求
	engine.Use(securityMiddleware.SuspiciousActivityDetection())
	engine.Use(securityMiddleware.OAuth2EventLogging())

	s := http.NewServer(
		engine,
		logger,
		http.WithServerHost(conf.GetString("http.host")),
		http.WithServerPort(conf.GetInt("http.port")),
	)
	// 注册OAuth2标准端点路由 - 必须在静态资源之前注
	setupOAuth2StandardRoutes(s, oauth2StandardHandler, oauth2ConsentHandler, oauth2ProductionHandler, appleHandler)

	// 设置前端静态资- 移到API路由之后
	s.Use(static.Serve("/", static.EmbedFolder(web.Assets(), "dist")))
	s.NoRoute(func(c *gin.Context) {
		// 检查是否为API端点,如果是则返04
		if isAPIEndpoint(c.Request.URL.Path) {
			c.String(nethttp.StatusNotFound, "404 page not found")
			return
		}

		// 对于前端路由,返回index.html
		indexPageData, err := web.Assets().ReadFile("dist/index.html")
		if err != nil {
			c.String(nethttp.StatusNotFound, "404 page not found")
			return
		}
		c.Data(nethttp.StatusOK, "text/html; charset=utf-8", indexPageData)
	})
	// swagger doc - 暂时注释掉
	// docs.SwaggerInfo.BasePath = "/"
	// s.GET("/swagger/*any", ginSwagger.WrapHandler(
	// 	swaggerfiles.Handler,
	// 	//ginSwagger.URL(fmt.Sprintf("http://localhost:%d/swagger/doc.json", conf.GetInt("app.http.port"))),
	// 	ginSwagger.DefaultModelsExpandDepth(-1),
	// 	ginSwagger.PersistAuthorization(true),
	// ))

	s.Use(
		middleware.CORSMiddleware(),
		middleware.ResponseLogMiddleware(logger),
		middleware.RequestLogMiddleware(logger),
		//middleware.SignMiddleware(log),
	)

	v1 := s.Group("/v1")
	{
		// No route group has permission
		noAuthRouter := v1.Group("/")
		{
			noAuthRouter.POST("/login", adminHandler.Login)
		}

		// Strict permission routing group
		strictAuthRouter := v1.Group("/").Use(middleware.StrictAuth(jwt, logger), middleware.AuthMiddleware(e))
		{
			strictAuthRouter.GET("/users", userHandler.GetUsers)

			strictAuthRouter.GET("/menus", adminHandler.GetMenus)
			strictAuthRouter.GET("/admin/menus", adminHandler.GetAdminMenus)
			strictAuthRouter.POST("/admin/menu", adminHandler.MenuCreate)
			strictAuthRouter.PUT("/admin/menu", adminHandler.MenuUpdate)
			strictAuthRouter.DELETE("/admin/menu", adminHandler.MenuDelete)

			strictAuthRouter.GET("/admin/users", adminHandler.GetAdminUsers)
			strictAuthRouter.GET("/admin/user", adminHandler.GetAdminUser)
			strictAuthRouter.PUT("/admin/user", adminHandler.AdminUserUpdate)
			strictAuthRouter.POST("/admin/user", adminHandler.AdminUserCreate)
			strictAuthRouter.DELETE("/admin/user", adminHandler.AdminUserDelete)
			strictAuthRouter.GET("/admin/user/permissions", adminHandler.GetUserPermissions)
			strictAuthRouter.GET("/admin/role/permissions", adminHandler.GetRolePermissions)
			strictAuthRouter.PUT("/admin/role/permission", adminHandler.UpdateRolePermission)
			strictAuthRouter.GET("/admin/roles", adminHandler.GetRoles)
			strictAuthRouter.POST("/admin/role", adminHandler.RoleCreate)
			strictAuthRouter.PUT("/admin/role", adminHandler.RoleUpdate)
			strictAuthRouter.DELETE("/admin/role", adminHandler.RoleDelete)

			strictAuthRouter.GET("/admin/apis", adminHandler.GetApis)
			strictAuthRouter.POST("/admin/api", adminHandler.ApiCreate)
			strictAuthRouter.PUT("/admin/api", adminHandler.ApiUpdate)
			strictAuthRouter.DELETE("/admin/api", adminHandler.ApiDelete)

		}
	}

	// 注册登录路由
	setupLoginRoutes(s, loginHandler)

	// 注册OAuth2管理路由
	setupOAuth2Routes(s, oauth2AdminHandler)

	// 注册SSF管理路由
	setupSSFManagementRoutes(s, ssfHandler)

	// 注册SSF标准端点路由
	setupSSFStandardRoutes(s, ssfStandardHandler)

	// 注册新的高级路由
	setupSSFAdvancedRoutes(s, ssfAdvancedHandler)

	return s
}

// isAPIEndpoint 检查是否为API端点
func isAPIEndpoint(path string) bool {
	apiPaths := []string{
		"/v1/",
		"/oauth2/",
		"/admin/",
		"/.well-known/",
		"/application/",
		"/swagger/",
	}

	for _, apiPath := range apiPaths {
		if len(path) >= len(apiPath) && path[:len(apiPath)] == apiPath {
			return true
		}
	}

	return false
}

// setupLoginRoutes 设置登录路由
func setupLoginRoutes(
	s *http.Server,
	loginHandler *handler.LoginHandler,
) {
	// 登录页面 - 公开访问
	s.GET("/login", loginHandler.ShowLoginPage)
	s.POST("/login", loginHandler.ProcessLogin)
	s.POST("/logout", loginHandler.Logout)

	// Login Hint演示端点 - 公开访问
	demoGroup := s.Group("/demo")
	{
		demoGroup.GET("/login-hint", loginHandler.DemoLoginHint)
		demoGroup.GET("/oauth2-flow", loginHandler.TestOAuth2Flow)
	}
}

// setupOAuth2Routes 设置OAuth2路由
func setupOAuth2Routes(
	s *http.Server,
	oauth2AdminHandler *handler.OAuth2AdminHandler,
) {

	// OAuth2管理路由
	adminGroup := s.Group("/admin/oauth2")
	adminGroup.Use(middleware.JWTAuth())
	adminGroup.Use(middleware.RequireRole("admin"))
	// adminGroup.Use(middleware.OAuth2RateLimitMiddleware()) // OAuth2专用限流
	{
		// 仪表
		adminGroup.GET("/dashboard", oauth2AdminHandler.GetDashboard)

		// Provider管理
		providerGroup := adminGroup.Group("/providers")
		{
			providerGroup.GET("", oauth2AdminHandler.GetProviders)
			providerGroup.POST("", oauth2AdminHandler.CreateProvider)
			providerGroup.GET("/:id", oauth2AdminHandler.GetProvider)
			providerGroup.PUT("/:id", oauth2AdminHandler.UpdateProvider)
			providerGroup.DELETE("/:id", oauth2AdminHandler.DeleteProvider)
			providerGroup.DELETE("/batch", oauth2AdminHandler.BatchDeleteProviders)
			providerGroup.POST("/import", oauth2AdminHandler.BatchImportProviders)
			providerGroup.POST("/:id/toggle-status", oauth2AdminHandler.ToggleProviderStatus)
			providerGroup.POST("/:id/regenerate-secret", oauth2AdminHandler.RegenerateSecret)
		}

		// 令牌管理
		tokenGroup := adminGroup.Group("/tokens")
		{
			tokenGroup.GET("/search", oauth2AdminHandler.SearchTokens)
			tokenGroup.GET("/analytics", oauth2AdminHandler.GetTokenAnalytics)
			tokenGroup.POST("/:tokenId/revoke", oauth2AdminHandler.RevokeToken)
			tokenGroup.POST("/:tokenId/extend", oauth2AdminHandler.ExtendTokenExpiry)
			tokenGroup.POST("/cleanup", oauth2AdminHandler.CleanupExpiredTokens)
		}

		// 用户授权管理
		authGroup := adminGroup.Group("/authorizations")
		{
			authGroup.GET("/search", oauth2AdminHandler.SearchUserAuthorizations)
			authGroup.GET("/analytics", oauth2AdminHandler.GetAuthorizationAnalytics)
			authGroup.POST("/:authorizationId/revoke", oauth2AdminHandler.RevokeUserAuthorization)
			authGroup.POST("/batch", oauth2AdminHandler.BatchRevokeUserAuthorizations)
		}

		// 审计日志
		auditGroup := adminGroup.Group("/audit")
		{
			auditGroup.GET("/logs/search", oauth2AdminHandler.SearchAuditLogs)
			auditGroup.GET("/logs/analytics", oauth2AdminHandler.GetAuditLogAnalytics)
			auditGroup.POST("/logs", oauth2AdminHandler.CreateAuditLog)
			auditGroup.POST("/logs/cleanup", oauth2AdminHandler.DeleteOldAuditLogs)
		}

		// 系统设置
		settingsGroup := adminGroup.Group("/settings")
		{
			settingsGroup.GET("/global", oauth2AdminHandler.GetGlobalSettings)
			settingsGroup.PUT("/global", oauth2AdminHandler.UpdateGlobalSettings)
			settingsGroup.GET("/security", oauth2AdminHandler.GetSecuritySettings)
			settingsGroup.PUT("/security", oauth2AdminHandler.UpdateSecuritySettings)
			settingsGroup.GET("/system/info", oauth2AdminHandler.GetSystemInfo)
			settingsGroup.GET("/system/health", oauth2AdminHandler.GetSystemHealth)
			settingsGroup.POST("/test/email", oauth2AdminHandler.TestEmailSettings)
			settingsGroup.POST("/test/cache", oauth2AdminHandler.TestCacheConnection)
			settingsGroup.POST("/test/database", oauth2AdminHandler.TestDatabaseConnection)
		}
	}
}

// setupSSFAdvancedRoutes 设置SSF高级路由
func setupSSFAdvancedRoutes(
	s *http.Server,
	ssfAdvancedHandler *handler.SSFHandler,
) {
	// SSF管理API路由
	ssfAPIGroup := s.Group("/api/ssf")
	ssfAPIGroup.Use(middleware.JWTAuth())
	{
		// SSF Provider管理
		providerGroup := ssfAPIGroup.Group("/providers")
		{
			providerGroup.POST("", ssfAdvancedHandler.CreateSSFProvider)
			providerGroup.GET("/:id", ssfAdvancedHandler.GetSSFProvider)
		}

		// Stream管理
		streamGroup := ssfAPIGroup.Group("/streams")
		{
			streamGroup.GET("", ssfAdvancedHandler.ListStreams)
			streamGroup.POST("", ssfAdvancedHandler.CreateStream)
			streamGroup.PUT("/:id", ssfAdvancedHandler.UpdateStream)
			streamGroup.DELETE("/:id", ssfAdvancedHandler.DeleteStream)
			streamGroup.GET("/:id/events", ssfAdvancedHandler.GetStreamEvents)
		}

		// 事件管理
		eventGroup := ssfAPIGroup.Group("/events")
		{
			eventGroup.POST("/user-logout", ssfAdvancedHandler.TriggerUserLogout)
			eventGroup.POST("/password-change", ssfAdvancedHandler.TriggerPasswordChange)
			eventGroup.POST("/cleanup", ssfAdvancedHandler.CleanupExpiredEvents)
		}

		// 配置和状
		ssfAPIGroup.GET("/configuration", ssfAdvancedHandler.GetSSFConfiguration)
		ssfAPIGroup.GET("/event-types", ssfAdvancedHandler.GetSupportedEventTypes)
		ssfAPIGroup.GET("/delivery-methods", ssfAdvancedHandler.GetSupportedDeliveryMethods)
	}
}

// setupSSFManagementRoutes 设置SSF管理路由
func setupSSFManagementRoutes(
	s *http.Server,
	ssfHandler *handler.SSFFixedHandler,
) {
	// 移除与标准端点冲突的路由,只保留管理功能
	// 标准端点由setupSSFStandardRoutes处理

	// SSF管理路由（管理员专用
	adminSSFGroup := s.Group("/admin/ssf")
	adminSSFGroup.Use(middleware.JWTAuth())
	adminSSFGroup.Use(middleware.RequireRole("admin"))
	{
		// SSF Provider管理
		providerGroup := adminSSFGroup.Group("/providers")
		{
			providerGroup.GET("", ssfHandler.GetSSFProviders)
			providerGroup.POST("", ssfHandler.CreateSSFProvider)
			providerGroup.GET("/:id", ssfHandler.GetSSFProvider)
			providerGroup.PUT("/:id", ssfHandler.UpdateSSFProvider)
			providerGroup.DELETE("/:id", ssfHandler.DeleteSSFProvider)
		}

		// SSF Stream管理
		streamGroup := adminSSFGroup.Group("/streams")
		{
			streamGroup.GET("", ssfHandler.GetSSFStreams)
			streamGroup.POST("", ssfHandler.CreateSSFStream)
			streamGroup.GET("/:id", ssfHandler.GetSSFStream)
			streamGroup.PUT("/:id", ssfHandler.UpdateSSFStream)
			streamGroup.DELETE("/:id", ssfHandler.DeleteSSFStream)
		}

		// SSF事件管理
		eventGroup := adminSSFGroup.Group("/events")
		{
			eventGroup.GET("", ssfHandler.GetSSFEvents)
		}

		// SSF统计信息
		adminSSFGroup.GET("/stats", ssfHandler.GetSSFStats)
	}
}

// setupOAuth2StandardRoutes 设置OAuth2标准端点路由（按照authentik模式）
func setupOAuth2StandardRoutes(
	s *http.Server,
	oauth2StandardHandler *handler.OAuth2StandardHandler,
	oauth2ConsentHandler *handler.OAuth2ConsentHandler,
	oauth2ProductionHandler *handler.OAuth2ProductionHandler,
	appleHandler *handler.AppleBusinessManagerHandler,
) {
	// 🔧 按照authentik模式：使用Apple Business Manager处理器替换关键端点
	// OpenID Connect Discovery端点 - 使用Apple处理器
	s.GET("/.well-known/openid_configuration", appleHandler.HandleAppleWellKnown)
	s.GET("/.well-known/ssf_configuration", appleHandler.HandleSSFConfiguration)
	s.GET("/.well-known/ssf_configuration/abm", appleHandler.HandleSSFConfiguration) // Authentik标准路径
	s.GET("/jwks", appleHandler.HandleJWKS)

	// 🔧 为兼容性保留/application/o/路径，但重定向到标准OAuth2路径
	applicationGroup := s.Group("/application/o")
	{
		// 重定向通用OAuth2端点到标准路径
		applicationGroup.GET("/authorize/", func(c *gin.Context) {
			// 保持查询参数
			query := c.Request.URL.RawQuery
			redirectURL := "/oauth2/authorize"
			if query != "" {
				redirectURL += "?" + query
			}
			c.Redirect(301, redirectURL) // 301 Moved Permanently
		})

		// 直接使用Apple Business Manager处理器
		applicationGroup.POST("/token/", appleHandler.HandleAppleToken)
		applicationGroup.GET("/userinfo/", appleHandler.HandleAppleUserInfo)

		applicationGroup.POST("/introspect/", func(c *gin.Context) {
			c.Redirect(308, "/oauth2/introspect") // 308 Permanent Redirect
		})

		applicationGroup.POST("/revoke/", func(c *gin.Context) {
			c.Redirect(308, "/oauth2/revoke") // 308 Permanent Redirect
		})

		// 应用特定JWKS端点 - 直接使用Apple处理器
		applicationGroup.GET("/:provider_id/jwks/", appleHandler.HandleJWKS)

		applicationGroup.GET("/:provider_id/end-session/", func(c *gin.Context) {
			c.Redirect(301, "/oauth2/end-session") // 301 Moved Permanently
		})

		applicationGroup.POST("/:provider_id/end-session/", func(c *gin.Context) {
			c.Redirect(308, "/oauth2/end-session") // 308 Permanent Redirect
		})

		applicationGroup.GET("/:provider_id/.well-known/openid-configuration", func(c *gin.Context) {
			c.Redirect(301, "/.well-known/openid-configuration") // 301 Moved Permanently
		})

		// 默认应用端点重定向
		applicationGroup.GET("/default/jwks/", func(c *gin.Context) {
			c.Redirect(301, "/oauth2/jwks") // 301 Moved Permanently
		})

		applicationGroup.GET("/default/end-session/", func(c *gin.Context) {
			c.Redirect(301, "/oauth2/end-session") // 301 Moved Permanently
		})

		applicationGroup.POST("/default/end-session/", func(c *gin.Context) {
			c.Redirect(308, "/oauth2/end-session") // 308 Permanent Redirect
		})

		applicationGroup.GET("/default/.well-known/openid-configuration", func(c *gin.Context) {
			c.Redirect(301, "/.well-known/openid-configuration") // 301 Moved Permanently
		})

		// // 根路径信息
		// applicationGroup.GET("/", func(ctx *gin.Context) {
		// 	ctx.JSON(nethttp.StatusOK, gin.H{
		// 		"message": "OAuth2 Provider - Redirected to standard endpoints",
		// 		"issuer":  "http://localhost:8080",
		// 		"standard_endpoints": gin.H{
		// 			"authorization_endpoint": "/oauth2/authorize",
		// 			"token_endpoint":         "/oauth2/token",
		// 			"userinfo_endpoint":      "/oauth2/userinfo",
		// 			"jwks_uri":               "/oauth2/jwks",
		// 			"discovery":              "/.well-known/openid-configuration",
		// 		},
		// 	})
		// })
	}

	// 🔧 保留原有的/oauth2/路径以确保向后兼容
	oauth2Group := s.Group("/oauth2")
	{
		// 用户信息端点 - 使用Apple处理器
		oauth2Group.GET("/userinfo", appleHandler.HandleAppleUserInfo)

		// JWKS端点 - 使用Apple处理器
		oauth2Group.GET("/jwks", appleHandler.HandleJWKS)

		// 授权端点 - 公开访问,但需要用户登录
		oauth2Group.GET("/authorize", oauth2StandardHandler.Authorize)

		// 令牌端点 - 公开访问,但需要客户端认证
		oauth2Group.POST("/token", oauth2StandardHandler.Token)

		// RFC 7009 - OAuth 2.0 Token Revocation
		oauth2Group.POST("/revoke", oauth2StandardHandler.Revoke)

		// RFC 7662 - OAuth 2.0 Token Introspection
		oauth2Group.POST("/introspect", oauth2StandardHandler.Introspect)

		// RFC 8628 - OAuth 2.0 Device Authorization Grant
		oauth2Group.POST("/device_authorization", oauth2StandardHandler.DeviceAuthorization)
		oauth2Group.GET("/device", oauth2StandardHandler.DeviceVerification)
		oauth2Group.POST("/device", oauth2StandardHandler.DeviceVerification)

		// OpenID Connect End Session端点
		oauth2Group.GET("/end-session", oauth2StandardHandler.EndSession)
		oauth2Group.POST("/end-session", oauth2StandardHandler.EndSession)

		// 用户同意页面路由
		oauth2Group.GET("/consent", oauth2ConsentHandler.ShowConsentPage)
		oauth2Group.POST("/consent/approve", oauth2ConsentHandler.ApproveConsent)
		oauth2Group.POST("/consent/deny", oauth2ConsentHandler.DenyConsent)
	}
}

// setupSSFStandardRoutes 设置SSF标准端点路由
func setupSSFStandardRoutes(
	s *http.Server,
	ssfStandardHandler *handler.SSFStandardHandler,
) {
	// SSF配置发现端点 - 公开访问,无需认证
	s.GET("/.well-known/ssf_configuration/:application_slug", ssfStandardHandler.SSFConfiguration)

	// SSF应用相关端点
	applicationGroup := s.Group("/application/ssf/:application_slug")
	{
		// JWKS端点 - 公开访问,无需认证
		applicationGroup.GET("/jwks/", ssfStandardHandler.SSFJWKs)

		// 事件流管理端- 需要适当的认
		applicationGroup.GET("/stream/", ssfStandardHandler.SSFStream)
		applicationGroup.POST("/stream/", ssfStandardHandler.SSFStream)
		applicationGroup.DELETE("/stream/", ssfStandardHandler.SSFStream)

		// 事件接收端点 - 需要JWT签名验证
		applicationGroup.POST("/events/", ssfStandardHandler.SSFEventReceiver)
	}
}

// isAppleBusinessManagerRequest 检测是否为Apple Business Manager请求
func isAppleBusinessManagerRequest(userAgent, clientIP string) bool {
	// 添加调试日志
	fmt.Printf("🔍 Apple检测 - User-Agent: %s, IP: %s\n", userAgent, clientIP)

	// 检查User-Agent是否包含Apple相关标识
	if strings.Contains(strings.ToLower(userAgent), "apple") {
		fmt.Printf("✅ Apple检测成功 - User-Agent包含Apple\n")
		return true
	}

	// 检查IP是否来自Apple的已知IP段
	appleIPPrefixes := []string{
		"57.103.",    // Apple Business Manager常用IP段
		"23.177.",    // Apple CDN IP段
		"17.0.",      // Apple公司IP段
		"2a01:b747:", // Apple IPv6段
	}

	for _, prefix := range appleIPPrefixes {
		if strings.HasPrefix(clientIP, prefix) {
			fmt.Printf("✅ Apple检测成功 - IP匹配前缀: %s\n", prefix)
			return true
		}
	}

	fmt.Printf("❌ Apple检测失败 - 不匹配任何条件\n")
	return false
}

// getClientRealIP 获取客户端真实IP（考虑各种代理头）
func getClientRealIP(c *gin.Context) string {
	// 优先检查Cloudflare头
	if ip := c.GetHeader("CF-Connecting-IP"); ip != "" {
		return ip
	}

	// 检查其他常见的代理头
	if ip := c.GetHeader("X-Forwarded-For"); ip != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		return strings.Split(strings.TrimSpace(ip), ",")[0]
	}

	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		return ip
	}

	if ip := c.GetHeader("X-Original-Forwarded-For"); ip != "" {
		return strings.Split(strings.TrimSpace(ip), ",")[0]
	}

	// 最后使用Gin的ClientIP
	return c.ClientIP()
}
