package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// EventService 基于Authentik模式的企业级事件记录服务
// 参考Authentik的Event模型和EventAction实现
type EventService struct {
	logger *zap.Logger
}

// NewEventService 创建事件服务
func NewEventService(logger *zap.Logger) *EventService {
	return &EventService{
		logger: logger,
	}
}

// EventAction 事件动作类型 (基于Authentik的EventAction)
type EventAction string

const (
	// 认证事件
	EventLogin       EventAction = "login"
	EventLoginFailed EventAction = "login_failed"
	EventLogout      EventAction = "logout"

	// OAuth2/OIDC事件
	EventAuthorizeApplication EventAction = "authorize_application"
	EventTokenIssued          EventAction = "token_issued"
	EventTokenRefreshed       EventAction = "token_refreshed"
	EventTokenRevoked         EventAction = "token_revoked"
	EventTokenIntrospected    EventAction = "token_introspected"

	// 安全事件
	EventSuspiciousRequest    EventAction = "suspicious_request"
	EventInvalidClient        EventAction = "invalid_client"
	EventPKCEValidationFailed EventAction = "pkce_validation_failed"
	EventRateLimitExceeded    EventAction = "rate_limit_exceeded"

	// 系统事件
	EventConfigurationError EventAction = "configuration_error"
	EventSystemStartup      EventAction = "system_startup"
	EventSystemShutdown     EventAction = "system_shutdown"

	// SSF事件
	EventSSFStreamCreated EventAction = "ssf_stream_created"
	EventSSFEventSent     EventAction = "ssf_event_sent"
	EventSSFEventFailed   EventAction = "ssf_event_failed"
)

// Event 事件结构 (基于Authentik的Event模型)
type Event struct {
	ID        string                 `json:"id"`
	Action    EventAction            `json:"action"`
	Timestamp time.Time              `json:"timestamp"`
	ClientIP  string                 `json:"client_ip,omitempty"`
	UserAgent string                 `json:"user_agent,omitempty"`
	UserID    string                 `json:"user_id,omitempty"`
	ClientID  string                 `json:"client_id,omitempty"`
	Context   map[string]interface{} `json:"context,omitempty"`
	Message   string                 `json:"message,omitempty"`
	Level     EventLevel             `json:"level"`
}

// EventLevel 事件级别
type EventLevel string

const (
	EventLevelInfo     EventLevel = "info"
	EventLevelWarning  EventLevel = "warning"
	EventLevelError    EventLevel = "error"
	EventLevelCritical EventLevel = "critical"
)

// EventContext 事件上下文构建器
type EventContext struct {
	data map[string]interface{}
}

// NewEventContext 创建事件上下文
func NewEventContext() *EventContext {
	return &EventContext{
		data: make(map[string]interface{}),
	}
}

// Set 设置上下文值
func (ec *EventContext) Set(key string, value interface{}) *EventContext {
	ec.data[key] = value
	return ec
}

// SetRequest 设置请求信息
func (ec *EventContext) SetRequest(c *gin.Context) *EventContext {
	if c != nil {
		ec.data["method"] = c.Request.Method
		ec.data["path"] = c.Request.URL.Path
		ec.data["query"] = c.Request.URL.RawQuery
		ec.data["content_length"] = c.Request.ContentLength
	}
	return ec
}

// SetOAuth2 设置OAuth2相关信息
func (ec *EventContext) SetOAuth2(grantType, scope, responseType string) *EventContext {
	if grantType != "" {
		ec.data["grant_type"] = grantType
	}
	if scope != "" {
		ec.data["scope"] = scope
	}
	if responseType != "" {
		ec.data["response_type"] = responseType
	}
	return ec
}

// SetError 设置错误信息
func (ec *EventContext) SetError(err error) *EventContext {
	if err != nil {
		ec.data["error"] = err.Error()
	}
	return ec
}

// Build 构建上下文数据
func (ec *EventContext) Build() map[string]interface{} {
	return ec.data
}

// RecordEvent 记录事件 (基于Authentik的Event.new方法)
func (s *EventService) RecordEvent(action EventAction, message string, context *EventContext) {
	event := &Event{
		ID:        s.generateEventID(),
		Action:    action,
		Timestamp: time.Now(),
		Message:   message,
		Level:     s.getEventLevel(action),
	}

	if context != nil {
		event.Context = context.Build()
	}

	s.logEvent(event)
}

// RecordEventWithRequest 记录带请求信息的事件
func (s *EventService) RecordEventWithRequest(c *gin.Context, action EventAction, message string, context *EventContext) {
	event := &Event{
		ID:        s.generateEventID(),
		Action:    action,
		Timestamp: time.Now(),
		ClientIP:  s.getClientIP(c),
		UserAgent: c.GetHeader("User-Agent"),
		Message:   message,
		Level:     s.getEventLevel(action),
	}

	// 提取OAuth2客户端ID
	if clientID := s.extractClientID(c); clientID != "" {
		event.ClientID = clientID
	}

	if context != nil {
		event.Context = context.Build()
	}

	s.logEvent(event)
}

// RecordOAuth2Event 记录OAuth2特定事件
func (s *EventService) RecordOAuth2Event(c *gin.Context, action EventAction, clientID, grantType string, success bool) {
	context := NewEventContext().
		SetRequest(c).
		SetOAuth2(grantType, "", "").
		Set("success", success)

	level := EventLevelInfo
	if !success {
		level = EventLevelWarning
	}

	message := fmt.Sprintf("OAuth2 %s for client %s", action, clientID)
	if !success {
		message += " failed"
	}

	event := &Event{
		ID:        s.generateEventID(),
		Action:    action,
		Timestamp: time.Now(),
		ClientIP:  s.getClientIP(c),
		UserAgent: c.GetHeader("User-Agent"),
		ClientID:  clientID,
		Message:   message,
		Level:     level,
		Context:   context.Build(),
	}

	s.logEvent(event)
}

// RecordSecurityEvent 记录安全事件
func (s *EventService) RecordSecurityEvent(c *gin.Context, action EventAction, reason string, severity EventLevel) {
	context := NewEventContext().
		SetRequest(c).
		Set("reason", reason).
		Set("severity", string(severity))

	message := fmt.Sprintf("Security event: %s - %s", action, reason)

	event := &Event{
		ID:        s.generateEventID(),
		Action:    action,
		Timestamp: time.Now(),
		ClientIP:  s.getClientIP(c),
		UserAgent: c.GetHeader("User-Agent"),
		Message:   message,
		Level:     severity,
		Context:   context.Build(),
	}

	s.logEvent(event)
}

// RecordSSFEvent 记录SSF事件
func (s *EventService) RecordSSFEvent(action EventAction, streamID, eventType string, success bool, details map[string]interface{}) {
	context := NewEventContext().
		Set("stream_id", streamID).
		Set("event_type", eventType).
		Set("success", success)

	for k, v := range details {
		context.Set(k, v)
	}

	level := EventLevelInfo
	if !success {
		level = EventLevelError
	}

	message := fmt.Sprintf("SSF %s for stream %s", action, streamID)
	if !success {
		message += " failed"
	}

	event := &Event{
		ID:        s.generateEventID(),
		Action:    action,
		Timestamp: time.Now(),
		Message:   message,
		Level:     level,
		Context:   context.Build(),
	}

	s.logEvent(event)
}

// logEvent 记录事件到日志系统
func (s *EventService) logEvent(event *Event) {
	// 序列化事件为JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to serialize event", zap.Error(err))
		return
	}

	// 根据事件级别选择日志级别
	switch event.Level {
	case EventLevelInfo:
		s.logger.Info("Event recorded",
			zap.String("event_id", event.ID),
			zap.String("action", string(event.Action)),
			zap.String("event_json", string(eventJSON)))
	case EventLevelWarning:
		s.logger.Warn("Event recorded",
			zap.String("event_id", event.ID),
			zap.String("action", string(event.Action)),
			zap.String("event_json", string(eventJSON)))
	case EventLevelError:
		s.logger.Error("Event recorded",
			zap.String("event_id", event.ID),
			zap.String("action", string(event.Action)),
			zap.String("event_json", string(eventJSON)))
	case EventLevelCritical:
		s.logger.Error("Critical event recorded",
			zap.String("event_id", event.ID),
			zap.String("action", string(event.Action)),
			zap.String("event_json", string(eventJSON)))
	}
}

// generateEventID 生成事件ID
func (s *EventService) generateEventID() string {
	return fmt.Sprintf("evt_%d", time.Now().UnixNano())
}

// getEventLevel 获取事件级别
func (s *EventService) getEventLevel(action EventAction) EventLevel {
	switch action {
	case EventLoginFailed, EventInvalidClient, EventPKCEValidationFailed:
		return EventLevelWarning
	case EventSuspiciousRequest, EventRateLimitExceeded, EventConfigurationError:
		return EventLevelError
	case EventSSFEventFailed:
		return EventLevelError
	default:
		return EventLevelInfo
	}
}

// getClientIP 获取客户端IP
func (s *EventService) getClientIP(c *gin.Context) string {
	// 检查X-Forwarded-For头
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		return xff
	}

	// 检查X-Real-IP头
	if xri := c.GetHeader("X-Real-IP"); xri != "" {
		return xri
	}

	// 使用RemoteAddr
	return c.ClientIP()
}

// extractClientID 从请求中提取客户端ID
func (s *EventService) extractClientID(c *gin.Context) string {
	// 从查询参数获取
	if clientID := c.Query("client_id"); clientID != "" {
		return clientID
	}

	// 从POST表单获取
	if clientID := c.PostForm("client_id"); clientID != "" {
		return clientID
	}

	// TODO: 从Authorization头解析Basic认证中的客户端ID

	return ""
}

// GetEventStats 获取事件统计 (用于监控)
func (s *EventService) GetEventStats(ctx context.Context, since time.Time) map[string]int {
	// TODO: 实现事件统计查询
	// 这里应该查询数据库或缓存获取统计信息
	return map[string]int{
		"total_events":    0,
		"login_events":    0,
		"oauth2_events":   0,
		"security_events": 0,
		"ssf_events":      0,
	}
}

// IsSecurityEvent 检查是否为安全事件
func (s *EventService) IsSecurityEvent(action EventAction) bool {
	securityEvents := []EventAction{
		EventLoginFailed,
		EventSuspiciousRequest,
		EventInvalidClient,
		EventPKCEValidationFailed,
		EventRateLimitExceeded,
	}

	for _, secEvent := range securityEvents {
		if action == secEvent {
			return true
		}
	}
	return false
}
