# Generated by Django 4.1.10 on 2023-07-06 12:51

import uuid

from django.db import migrations, models

import authentik.enterprise.models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="License",
            fields=[
                (
                    "license_uuid",
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ("key", models.TextField(unique=True)),
                ("name", models.TextField()),
                ("expiry", models.DateTimeField()),
                ("users", models.BigIntegerField()),
                ("external_users", models.BigIntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="LicenseUsage",
            fields=[
                ("expiring", models.BooleanField(default=True)),
                ("expires", models.DateTimeField(default=authentik.enterprise.models.usage_expiry)),
                (
                    "usage_uuid",
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ("user_count", models.BigIntegerField()),
                ("external_user_count", models.BigIntegerField()),
                ("within_limits", models.BooleanField()),
                ("record_date", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
