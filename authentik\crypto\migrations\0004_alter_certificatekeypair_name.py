# Generated by Django 4.1.7 on 2023-04-28 10:49

from django.db import migrations, models

from authentik.lib.migrations import fallback_names


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_crypto", "0003_certificatekeypair_managed"),
    ]

    operations = [
        migrations.RunPython(fallback_names("authentik_crypto", "certificatekeypair", "name")),
        migrations.AlterField(
            model_name="certificatekeypair",
            name="name",
            field=models.TextField(unique=True),
        ),
        migrations.AlterField(
            model_name="certificatekeypair",
            name="managed",
            field=models.TextField(
                default=None,
                help_text="Objects that are managed by authentik. These objects are created and updated automatically. This flag only indicates that an object can be overwritten by migrations. You can still modify the objects via the API, but expect changes to be overwritten in a later update.",
                null=True,
                unique=True,
                verbose_name="Managed by authentik",
            ),
        ),
    ]
