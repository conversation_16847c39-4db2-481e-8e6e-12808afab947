#!/bin/bash

# Apple Business Manager错误码-27481解决方案
# 使用Cloudflare Tunnel将本地HTTP服务器暴露为HTTPS

echo "🍎 Apple Business Manager错误码-27481解决方案"
echo "🔧 设置Cloudflare Tunnel将localhost:8000暴露为HTTPS"
echo ""

# 检查cloudflared是否已安装
if ! command -v cloudflared &> /dev/null; then
    echo "❌ cloudflared未安装，请先安装："
    echo "   Windows: winget install --id Cloudflare.cloudflared"
    echo "   macOS: brew install cloudflared"
    echo "   Linux: 从 https://github.com/cloudflare/cloudflared/releases 下载"
    exit 1
fi

echo "✅ cloudflared已安装"

# 创建隧道配置
echo "📝 创建Cloudflare隧道配置..."

# 创建配置目录
mkdir -p ~/.cloudflared

# 创建隧道配置文件
cat > ~/.cloudflared/config.yml << EOF
# Apple Business Manager HTTPS隧道配置
tunnel: apple-idp-tunnel
credentials-file: ~/.cloudflared/credentials.json

ingress:
  - hostname: openid.akapril.in
    service: http://localhost:8000
    originRequest:
      httpHostHeader: localhost:8000
      noTLSVerify: true
  - service: http_status:404
EOF

echo "✅ 隧道配置文件已创建: ~/.cloudflared/config.yml"

# 显示下一步说明
echo ""
echo "🚀 下一步操作："
echo ""
echo "1. 登录Cloudflare并创建隧道："
echo "   cloudflared tunnel login"
echo ""
echo "2. 创建名为'apple-idp-tunnel'的隧道："
echo "   cloudflared tunnel create apple-idp-tunnel"
echo ""
echo "3. 配置DNS记录："
echo "   cloudflared tunnel route dns apple-idp-tunnel openid.akapril.in"
echo ""
echo "4. 启动隧道："
echo "   cloudflared tunnel run apple-idp-tunnel"
echo ""
echo "5. 在另一个终端启动go-abm-idp服务器："
echo "   go run cmd/server/main.go"
echo ""
echo "6. 测试HTTPS端点："
echo "   curl https://openid.akapril.in/.well-known/openid_configuration"
echo ""
echo "🎯 完成后，Apple Business Manager将能够通过HTTPS访问您的服务器！"
echo ""
echo "📋 Apple Business Manager配置："
echo "   发行者URL: https://openid.akapril.in"
echo "   客户端ID: go-abm-idp-client"
echo "   客户端密钥: ABM-GoIDP-2024-SecureSecret-Production"
echo ""
