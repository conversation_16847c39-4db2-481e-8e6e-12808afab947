package service

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AppleUserSyncService Apple Business Manager用户同步服务
// 基于Apple Business Manager API规范实现用户列表同步
type AppleUserSyncService struct {
	logger       *zap.Logger
	eventService *EventService
}

// NewAppleUserSyncService 创建Apple用户同步服务
func NewAppleUserSyncService(logger *zap.Logger, eventService *EventService) *AppleUserSyncService {
	return &AppleUserSyncService{
		logger:       logger,
		eventService: eventService,
	}
}

// AppleUserSyncRequest Apple Business Manager用户同步请求
type AppleUserSyncRequest struct {
	// Apple Business Manager请求的权限范围
	Permissions []string `json:"permissions" binding:"required"`

	// 同步类型: "full" | "incremental" | "delta"
	SyncType string `json:"sync_type" binding:"required"`

	// 上次同步时间戳 (用于增量同步)
	LastSyncTime *time.Time `json:"last_sync_time,omitempty"`

	// 请求的用户属性
	RequestedAttributes []string `json:"requested_attributes,omitempty"`

	// 分页参数
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// AppleUserSyncResponse Apple Business Manager用户同步响应
type AppleUserSyncResponse struct {
	// 同步状态
	Status string `json:"status"` // "success" | "partial" | "error"

	// 用户列表
	Users []AppleSyncUser `json:"users"`

	// 同步元数据
	Metadata AppleSyncMetadata `json:"metadata"`

	// 错误信息 (如果有)
	Errors []AppleSyncError `json:"errors,omitempty"`
}

// AppleSyncUser Apple Business Manager同步用户信息
type AppleSyncUser struct {
	// 用户唯一标识符
	UserID string `json:"user_id"`

	// 用户名
	Username string `json:"username"`

	// 邮箱地址
	Email string `json:"email"`

	// 显示名称
	DisplayName string `json:"display_name,omitempty"`

	// 名字
	GivenName string `json:"given_name,omitempty"`

	// 姓氏
	FamilyName string `json:"family_name,omitempty"`

	// 用户状态: "active" | "inactive" | "suspended"
	Status string `json:"status"`

	// 用户角色
	Roles []string `json:"roles,omitempty"`

	// 部门/组织
	Department string `json:"department,omitempty"`

	// 最后登录时间
	LastLoginTime *time.Time `json:"last_login_time,omitempty"`

	// 密码最后更改时间
	PasswordChangedTime *time.Time `json:"password_changed_time,omitempty"`

	// 账户创建时间
	CreatedTime time.Time `json:"created_time"`

	// 账户更新时间
	UpdatedTime time.Time `json:"updated_time"`

	// 是否需要重新认证 (密码已更改)
	RequiresReauth bool `json:"requires_reauth"`
}

// AppleSyncMetadata 同步元数据
type AppleSyncMetadata struct {
	// 同步时间戳
	SyncTimestamp time.Time `json:"sync_timestamp"`

	// 总用户数
	TotalUsers int `json:"total_users"`

	// 返回的用户数
	ReturnedUsers int `json:"returned_users"`

	// 是否有更多数据
	HasMore bool `json:"has_more"`

	// 下一页偏移量
	NextOffset int `json:"next_offset,omitempty"`

	// 同步类型
	SyncType string `json:"sync_type"`

	// 权限列表
	GrantedPermissions []string `json:"granted_permissions"`
}

// AppleSyncError 同步错误信息
type AppleSyncError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	UserID  string `json:"user_id,omitempty"`
}

// HandleUserSyncRequest 处理Apple Business Manager用户同步请求
func (s *AppleUserSyncService) HandleUserSyncRequest(c *gin.Context) {
	var request AppleUserSyncRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		s.logger.Error("Invalid user sync request", zap.Error(err))
		c.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid user sync request format",
		})
		return
	}

	// 验证同步类型
	if !s.isValidSyncType(request.SyncType) {
		s.logger.Error("Invalid sync type", zap.String("sync_type", request.SyncType))
		c.JSON(400, gin.H{
			"error":             "invalid_sync_type",
			"error_description": "Supported sync types: full, incremental, delta",
		})
		return
	}

	// 验证分页参数
	if request.Limit < 0 || request.Limit > 1000 {
		s.logger.Error("Invalid limit parameter", zap.Int("limit", request.Limit))
		c.JSON(400, gin.H{
			"error":             "invalid_limit",
			"error_description": "Limit must be between 0 and 1000",
		})
		return
	}

	if request.Offset < 0 {
		s.logger.Error("Invalid offset parameter", zap.Int("offset", request.Offset))
		c.JSON(400, gin.H{
			"error":             "invalid_offset",
			"error_description": "Offset must be non-negative",
		})
		return
	}

	// 记录同步请求事件
	s.eventService.RecordSecurityEvent(c,
		EventUserSync,
		fmt.Sprintf("Apple Business Manager user sync requested: type=%s, permissions=%v",
			request.SyncType, request.Permissions),
		EventLevelInfo)

	// 验证权限
	if !s.validateSyncPermissions(request.Permissions) {
		c.Header("WWW-Authenticate", "Bearer realm=\"Apple Business Manager\", error=\"insufficient_scope\"")
		c.JSON(403, gin.H{
			"error":             "insufficient_scope",
			"error_description": "The request requires higher privileges than provided by the access token",
			"error_uri":         "https://tools.ietf.org/html/rfc6750#section-3.1",
		})
		return
	}

	// 执行用户同步
	response, err := s.performUserSync(c.Request.Context(), &request)
	if err != nil {
		s.logger.Error("User sync failed", zap.Error(err))
		c.JSON(500, gin.H{
			"error":             "sync_failed",
			"error_description": "User synchronization failed",
		})
		return
	}

	// 记录同步完成事件
	s.eventService.RecordSecurityEvent(c,
		EventUserSync,
		fmt.Sprintf("User sync completed: status=%s, users=%d",
			response.Status, len(response.Users)),
		EventLevelInfo)

	c.JSON(200, response)
}

// validateSyncPermissions 验证同步权限
func (s *AppleUserSyncService) validateSyncPermissions(permissions []string) bool {
	// Apple Business Manager支持的权限 (基于SCIM 2.0和Apple Business Manager API规范)
	supportedPermissions := map[string]bool{
		"read_users":           true, // 基础用户信息读取
		"read_user_profiles":   true, // 用户详细档案读取
		"read_user_status":     true, // 用户状态信息读取
		"read_user_roles":      true, // 用户角色信息读取
		"read_login_history":   true, // 登录历史记录读取
		"read_password_status": true, // 密码状态信息读取
		"read_user_groups":     true, // 用户组信息读取 (新增)
		"read_user_attributes": true, // 用户属性信息读取 (新增)
	}

	// 验证权限列表不为空
	if len(permissions) == 0 {
		s.logger.Warn("No permissions requested")
		return false
	}

	// 验证权限数量限制 (防止权限滥用)
	if len(permissions) > 10 {
		s.logger.Warn("Too many permissions requested", zap.Int("count", len(permissions)))
		return false
	}

	// 验证每个权限
	for _, permission := range permissions {
		if !supportedPermissions[permission] {
			s.logger.Warn("Unsupported permission requested",
				zap.String("permission", permission),
				zap.Strings("supported_permissions", s.getSupportedPermissions()))
			return false
		}
	}

	return true
}

// getSupportedPermissions 获取支持的权限列表
func (s *AppleUserSyncService) getSupportedPermissions() []string {
	return []string{
		"read_users",
		"read_user_profiles",
		"read_user_status",
		"read_user_roles",
		"read_login_history",
		"read_password_status",
		"read_user_groups",
		"read_user_attributes",
	}
}

// isValidSyncType 验证同步类型是否有效
func (s *AppleUserSyncService) isValidSyncType(syncType string) bool {
	validSyncTypes := map[string]bool{
		"full":        true, // 全量同步
		"incremental": true, // 增量同步
		"delta":       true, // 差异同步
	}
	return validSyncTypes[syncType]
}

// performUserSync 执行用户同步
func (s *AppleUserSyncService) performUserSync(ctx context.Context, request *AppleUserSyncRequest) (*AppleUserSyncResponse, error) {
	// 模拟用户数据 (实际实现中应该从数据库获取)
	users := s.getMockUsers(request)

	// 过滤需要重新认证的用户
	for i := range users {
		users[i].RequiresReauth = s.checkPasswordChanged(&users[i])
	}

	response := &AppleUserSyncResponse{
		Status: "success",
		Users:  users,
		Metadata: AppleSyncMetadata{
			SyncTimestamp:      time.Now(),
			TotalUsers:         len(users),
			ReturnedUsers:      len(users),
			HasMore:            false,
			SyncType:           request.SyncType,
			GrantedPermissions: request.Permissions,
		},
	}

	return response, nil
}

// getMockUsers 获取模拟用户数据 (实际实现中应该从数据库获取)
func (s *AppleUserSyncService) getMockUsers(request *AppleUserSyncRequest) []AppleSyncUser {
	now := time.Now()

	users := []AppleSyncUser{
		{
			UserID:              "user_001",
			Username:            "john.doe",
			Email:               "<EMAIL>",
			DisplayName:         "John Doe",
			GivenName:           "John",
			FamilyName:          "Doe",
			Status:              "active",
			Roles:               []string{"employee", "manager"},
			Department:          "Engineering",
			LastLoginTime:       &now,
			PasswordChangedTime: &now,
			CreatedTime:         now.AddDate(0, -6, 0),
			UpdatedTime:         now,
			RequiresReauth:      false,
		},
		{
			UserID:              "user_002",
			Username:            "jane.smith",
			Email:               "<EMAIL>",
			DisplayName:         "Jane Smith",
			GivenName:           "Jane",
			FamilyName:          "Smith",
			Status:              "active",
			Roles:               []string{"employee"},
			Department:          "Marketing",
			LastLoginTime:       &now,
			PasswordChangedTime: &now,
			CreatedTime:         now.AddDate(0, -3, 0),
			UpdatedTime:         now,
			RequiresReauth:      false,
		},
	}

	// 应用分页
	if request.Limit > 0 {
		start := request.Offset
		end := start + request.Limit
		if start < len(users) {
			if end > len(users) {
				end = len(users)
			}
			users = users[start:end]
		} else {
			users = []AppleSyncUser{}
		}
	}

	return users
}

// checkPasswordChanged 检查用户密码是否已更改
func (s *AppleUserSyncService) checkPasswordChanged(user *AppleSyncUser) bool {
	// 实际实现中应该检查密码更改时间戳
	// 如果密码在最后一次Apple Business Manager同步后更改，则需要重新认证

	// 模拟逻辑：如果密码更改时间在24小时内，则需要重新认证
	if user.PasswordChangedTime != nil {
		return time.Since(*user.PasswordChangedTime) < 24*time.Hour
	}

	return false
}

// HandlePasswordChangeNotification 处理密码更改通知
func (s *AppleUserSyncService) HandlePasswordChangeNotification(c *gin.Context) {
	var notification struct {
		UserID    string    `json:"user_id" binding:"required"`
		Email     string    `json:"email" binding:"required"`
		Timestamp time.Time `json:"timestamp" binding:"required"`
	}

	if err := c.ShouldBindJSON(&notification); err != nil {
		c.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid password change notification",
		})
		return
	}

	// 记录密码更改事件
	s.eventService.RecordSecurityEvent(c,
		EventPasswordChange,
		fmt.Sprintf("Password changed for user %s (%s)", notification.UserID, notification.Email),
		EventLevelInfo)

	// 触发SSF密码更改事件 (通知Apple Business Manager)
	TriggerPasswordChangeEvent(notification.Email, "update")

	s.logger.Info("Password change notification processed",
		zap.String("user_id", notification.UserID),
		zap.String("email", notification.Email))

	c.JSON(200, gin.H{
		"status":  "success",
		"message": "Password change notification processed",
	})
}
