# Generated by Django 5.0.4 on 2024-04-18 11:29

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    replaces = [
        ("authentik_stages_authenticator_webauthn", "0001_initial"),
        ("authentik_stages_authenticator_webauthn", "0002_default_setup_flow"),
        ("authentik_stages_authenticator_webauthn", "0003_webauthndevice_confirmed"),
        ("authentik_stages_authenticator_webauthn", "0004_auto_20210304_1850"),
        (
            "authentik_stages_authenticator_webauthn",
            "0005_authenticatewebauthnstage_user_verification",
        ),
        (
            "authentik_stages_authenticator_webauthn",
            "0006_authenticatewebauthnstage_authenticator_attachment_and_more",
        ),
        (
            "authentik_stages_authenticator_webauthn",
            "0007_rename_last_used_on_webauthndevice_last_t",
        ),
        ("authentik_stages_authenticator_webauthn", "0008_alter_webauthndevice_credential_id"),
        ("authentik_stages_authenticator_webauthn", "0009_authenticatewebauthnstage_friendly_name"),
        (
            "authentik_stages_authenticator_webauthn",
            "0010_webauthndevicetype_authenticatorwebauthnstage_and_more",
        ),
        ("authentik_stages_authenticator_webauthn", "0011_webauthndevice_aaguid"),
    ]

    initial = True

    dependencies = [
        ("authentik_flows", "0016_auto_20201202_1307"),
        ("authentik_flows", "0027_auto_20231028_1424"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WebAuthnDeviceType",
            fields=[
                ("aaguid", models.UUIDField(primary_key=True, serialize=False, unique=True)),
                ("description", models.TextField()),
                ("icon", models.TextField(null=True)),
            ],
            options={
                "verbose_name": "WebAuthn Device type",
                "verbose_name_plural": "WebAuthn Device types",
            },
        ),
        migrations.CreateModel(
            name="AuthenticatorWebAuthnStage",
            fields=[
                (
                    "stage_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_flows.stage",
                    ),
                ),
                (
                    "configure_flow",
                    models.ForeignKey(
                        blank=True,
                        help_text="Flow used by an authenticated user to configure this Stage. If empty, user will not be able to configure this stage.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="authentik_flows.flow",
                    ),
                ),
                (
                    "user_verification",
                    models.TextField(
                        choices=[
                            ("required", "Required"),
                            ("preferred", "Preferred"),
                            ("discouraged", "Discouraged"),
                        ],
                        default="preferred",
                    ),
                ),
                (
                    "authenticator_attachment",
                    models.TextField(
                        choices=[("platform", "Platform"), ("cross-platform", "Cross Platform")],
                        default=None,
                        null=True,
                    ),
                ),
                (
                    "resident_key_requirement",
                    models.TextField(
                        choices=[
                            ("discouraged", "Discouraged"),
                            ("preferred", "Preferred"),
                            ("required", "Required"),
                        ],
                        default="preferred",
                    ),
                ),
                ("friendly_name", models.TextField(null=True)),
                (
                    "device_type_restrictions",
                    models.ManyToManyField(
                        blank=True, to="authentik_stages_authenticator_webauthn.webauthndevicetype"
                    ),
                ),
            ],
            options={
                "verbose_name": "WebAuthn Authenticator Setup Stage",
                "verbose_name_plural": "WebAuthn Authenticator Setup Stages",
            },
            bases=("authentik_flows.stage", models.Model),
        ),
        migrations.CreateModel(
            name="WebAuthnDevice",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("name", models.TextField(max_length=200)),
                ("credential_id", models.TextField(unique=True)),
                ("public_key", models.TextField()),
                ("sign_count", models.IntegerField(default=0)),
                ("rp_id", models.CharField(max_length=253)),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("last_t", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "confirmed",
                    models.BooleanField(default=True, help_text="Is this device ready for use?"),
                ),
                (
                    "device_type",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_DEFAULT,
                        to="authentik_stages_authenticator_webauthn.webauthndevicetype",
                    ),
                ),
                ("aaguid", models.TextField(default="00000000-0000-0000-0000-000000000000")),
            ],
            options={
                "verbose_name": "WebAuthn Device",
                "verbose_name_plural": "WebAuthn Devices",
            },
        ),
    ]
