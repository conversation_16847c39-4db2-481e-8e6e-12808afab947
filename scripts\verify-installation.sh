#!/bin/bash

# Go ABM IDP 安装验证脚本
# 用于验证所有核心功能是否正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="${BASE_URL:-http://localhost:8080}"
ADMIN_TOKEN="${ADMIN_TOKEN:-}"

echo -e "${BLUE}=== Go ABM IDP 安装验证 ===${NC}"
echo "Base URL: $BASE_URL"
echo ""

# 检查函数
check_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -n "检查 $description... "
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/response "$BASE_URL$endpoint" 2>/dev/null); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            echo -e "${GREEN}✓${NC}"
            return 0
        else
            echo -e "${RED}✗ (状态码: $status_code)${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ (连接失败)${NC}"
        return 1
    fi
}

check_json_endpoint() {
    local endpoint=$1
    local description=$2
    local json_key=$3
    
    echo -n "检查 $description... "
    
    if response=$(curl -s "$BASE_URL$endpoint" 2>/dev/null); then
        if echo "$response" | jq -e ".$json_key" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC}"
            return 0
        else
            echo -e "${RED}✗ (JSON格式错误)${NC}"
            echo "响应: $response"
            return 1
        fi
    else
        echo -e "${RED}✗ (连接失败)${NC}"
        return 1
    fi
}

# 1. 基础健康检查
echo -e "${YELLOW}1. 基础健康检查${NC}"
check_endpoint "/health" "服务健康状态"
check_endpoint "/ready" "服务就绪状态"
echo ""

# 2. OIDC发现端点
echo -e "${YELLOW}2. OIDC发现端点${NC}"
check_json_endpoint "/.well-known/openid-configuration" "OIDC配置" "issuer"
check_json_endpoint "/.well-known/openid-configuration" "授权端点" "authorization_endpoint"
check_json_endpoint "/.well-known/openid-configuration" "令牌端点" "token_endpoint"
check_json_endpoint "/.well-known/openid-configuration" "用户信息端点" "userinfo_endpoint"
echo ""

# 3. JWKS端点
echo -e "${YELLOW}3. JWKS端点${NC}"
check_json_endpoint "/.well-known/jwks.json" "JWKS密钥集" "keys"
echo ""

# 4. OAuth2端点基础检查
echo -e "${YELLOW}4. OAuth2端点${NC}"
check_endpoint "/oauth2/authorize" "授权端点" 400  # 期望400因为缺少参数
check_endpoint "/oauth2/token" "令牌端点" 400     # 期望400因为缺少参数
check_endpoint "/oauth2/userinfo" "用户信息端点" 401  # 期望401因为未认证
echo ""

# 5. 管理API检查 (如果有admin token)
if [ -n "$ADMIN_TOKEN" ]; then
    echo -e "${YELLOW}5. 管理API${NC}"
    
    # 检查OAuth2客户端管理
    echo -n "检查客户端管理API... "
    if response=$(curl -s -H "Authorization: Bearer $ADMIN_TOKEN" "$BASE_URL/admin/providers" 2>/dev/null); then
        if echo "$response" | jq -e '.code' >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC}"
        else
            echo -e "${RED}✗${NC}"
        fi
    else
        echo -e "${RED}✗${NC}"
    fi
    
    # 检查性能监控API
    echo -n "检查性能监控API... "
    if response=$(curl -s -H "Authorization: Bearer $ADMIN_TOKEN" "$BASE_URL/admin/performance/metrics" 2>/dev/null); then
        if echo "$response" | jq -e '.data' >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC}"
        else
            echo -e "${RED}✗${NC}"
        fi
    else
        echo -e "${RED}✗${NC}"
    fi
    echo ""
else
    echo -e "${YELLOW}5. 管理API (跳过 - 未提供ADMIN_TOKEN)${NC}"
    echo ""
fi

# 6. 数据库连接检查
echo -e "${YELLOW}6. 数据库连接${NC}"
echo -n "检查数据库连接... "
if go run cmd/server/main.go --test-db >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗${NC}"
fi
echo ""

# 7. 配置验证
echo -e "${YELLOW}7. 配置验证${NC}"
echo -n "检查配置文件... "
if go run cmd/server/main.go --config-check >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗${NC}"
fi
echo ""

# 8. 性能基准测试
echo -e "${YELLOW}8. 性能基准测试${NC}"
echo "运行基础性能测试..."

# OIDC配置端点性能
echo -n "OIDC配置端点响应时间: "
time_result=$(curl -w "%{time_total}" -s -o /dev/null "$BASE_URL/.well-known/openid-configuration" 2>/dev/null)
echo "${time_result}s"

# JWKS端点性能
echo -n "JWKS端点响应时间: "
time_result=$(curl -w "%{time_total}" -s -o /dev/null "$BASE_URL/.well-known/jwks.json" 2>/dev/null)
echo "${time_result}s"

echo ""

# 9. 安全检查
echo -e "${YELLOW}9. 安全检查${NC}"

# 检查HTTPS重定向 (如果是生产环境)
if [[ "$BASE_URL" == https://* ]]; then
    echo -n "检查HTTPS配置... "
    if curl -s -I "$BASE_URL" | grep -q "Strict-Transport-Security"; then
        echo -e "${GREEN}✓ (HSTS已启用)${NC}"
    else
        echo -e "${YELLOW}⚠ (建议启用HSTS)${NC}"
    fi
else
    echo -e "${YELLOW}⚠ 当前使用HTTP，生产环境建议使用HTTPS${NC}"
fi

# 检查安全头
echo -n "检查安全响应头... "
headers=$(curl -s -I "$BASE_URL/.well-known/openid-configuration")
security_score=0

if echo "$headers" | grep -q "X-Content-Type-Options"; then
    ((security_score++))
fi
if echo "$headers" | grep -q "X-Frame-Options"; then
    ((security_score++))
fi
if echo "$headers" | grep -q "X-XSS-Protection"; then
    ((security_score++))
fi

if [ $security_score -ge 2 ]; then
    echo -e "${GREEN}✓ ($security_score/3 安全头已设置)${NC}"
else
    echo -e "${YELLOW}⚠ ($security_score/3 安全头已设置，建议完善)${NC}"
fi

echo ""

# 10. 总结
echo -e "${BLUE}=== 验证完成 ===${NC}"
echo ""
echo -e "${GREEN}✓ 表示功能正常${NC}"
echo -e "${YELLOW}⚠ 表示需要注意${NC}"
echo -e "${RED}✗ 表示存在问题${NC}"
echo ""

# 提供下一步建议
echo -e "${BLUE}下一步建议:${NC}"
echo "1. 如果所有检查都通过，可以开始配置OAuth2客户端"
echo "2. 参考 docs/api/README.md 了解完整的API使用方法"
echo "3. 查看 docs/deployment/README.md 了解生产环境部署"
echo "4. 使用 docs/security/README.md 加强安全配置"
echo ""

echo "验证脚本完成！"
