# JWKS Endpoint Test for Apple Business Manager Error Code -27480

param(
    [string]$BaseURL = "http://localhost:8000"
)

Write-Host "🔑 JWKS Endpoint Test for Apple Business Manager" -ForegroundColor Green
Write-Host "Target URL: $BaseURL" -ForegroundColor Cyan
Write-Host "Test Time: $(Get-Date)" -ForegroundColor Gray

$Results = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-JWKSEndpoint {
    param(
        [string]$TestName,
        [string]$URL,
        [scriptblock]$ValidationScript
    )
    
    $Results.Total++
    Write-Host "`n🔍 Testing: $TestName" -ForegroundColor Yellow
    Write-Host "   URL: $URL" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $URL -UseBasicParsing -ErrorAction Stop
        $result = & $ValidationScript $response
        
        if ($result.Success) {
            Write-Host "   ✅ PASS: $($result.Message)" -ForegroundColor Green
            $Results.Passed++
        } else {
            Write-Host "   ❌ FAIL: $($result.Message)" -ForegroundColor Red
            $Results.Failed++
        }
        
        if ($result.Details) {
            foreach ($detail in $result.Details) {
                Write-Host "     - $detail" -ForegroundColor Gray
            }
        }
        
    } catch {
        Write-Host "   ❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        $Results.Failed++
    }
}

# Test 1: JWKS Endpoint Accessibility
Test-JWKSEndpoint -TestName "JWKS Endpoint Accessibility" -URL "$BaseURL/oauth2/jwks" -ValidationScript {
    param($response)
    
    $details = @()
    $issues = @()
    
    # Check HTTP status
    if ($response.StatusCode -eq 200) {
        $details += "✓ HTTP Status: 200 OK"
    } else {
        $issues += "✗ HTTP Status: $($response.StatusCode)"
    }
    
    # Check Content-Type
    $contentType = $response.Headers["Content-Type"]
    if ($contentType -and $contentType.StartsWith("application/json")) {
        $details += "✓ Content-Type: $contentType"
    } else {
        $issues += "✗ Content-Type: $contentType (should be application/json)"
    }
    
    # Check CORS headers
    $corsOrigin = $response.Headers["Access-Control-Allow-Origin"]
    if ($corsOrigin) {
        $details += "✓ CORS Origin: $corsOrigin"
    } else {
        $issues += "⚠ CORS Origin header missing"
    }
    
    $success = $issues.Count -eq 0
    $message = if ($success) { "JWKS endpoint accessible" } else { "JWKS endpoint issues found" }
    
    return @{ Success = $success; Message = $message; Details = $details }
}

# Test 2: JWKS JSON Structure Validation
Test-JWKSEndpoint -TestName "JWKS JSON Structure Validation" -URL "$BaseURL/oauth2/jwks" -ValidationScript {
    param($response)
    
    try {
        $jwks = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        # Check top-level structure
        if ($jwks.keys) {
            $details += "✓ JWKS keys array present"
            
            if ($jwks.keys.Count -gt 0) {
                $details += "✓ Keys count: $($jwks.keys.Count)"
                
                $key = $jwks.keys[0]
                
                # Check required JWK fields (RFC 7517)
                $requiredFields = @("kty", "use", "kid", "alg", "n", "e")
                foreach ($field in $requiredFields) {
                    if ($key.$field) {
                        $details += "✓ $field: $($key.$field)"
                    } else {
                        $issues += "✗ Missing required field: $field"
                    }
                }
                
                # Validate field values
                if ($key.kty -eq "RSA") {
                    $details += "✓ Key type: RSA"
                } else {
                    $issues += "✗ Invalid key type: $($key.kty) (should be RSA)"
                }
                
                if ($key.use -eq "sig") {
                    $details += "✓ Key use: sig"
                } else {
                    $issues += "✗ Invalid key use: $($key.use) (should be sig)"
                }
                
                if ($key.alg -eq "RS256") {
                    $details += "✓ Algorithm: RS256"
                } else {
                    $issues += "✗ Invalid algorithm: $($key.alg) (should be RS256)"
                }
                
                # Check n and e parameters
                if ($key.n -and $key.n.Length -gt 0) {
                    $details += "✓ RSA modulus (n): present ($($key.n.Length) chars)"
                } else {
                    $issues += "✗ RSA modulus (n): missing or empty"
                }
                
                if ($key.e -and $key.e.Length -gt 0) {
                    $details += "✓ RSA exponent (e): present ($($key.e.Length) chars)"
                } else {
                    $issues += "✗ RSA exponent (e): missing or empty"
                }
                
                # Check for Apple Business Manager specific requirements
                if ($key.kid -and $key.kid.Contains("apple")) {
                    $details += "✓ Key ID contains 'apple': $($key.kid)"
                } else {
                    $details += "⚠ Key ID doesn't contain 'apple': $($key.kid)"
                }
                
            } else {
                $issues += "✗ No keys in JWKS"
            }
        } else {
            $issues += "✗ JWKS keys array missing"
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "JWKS structure valid" } else { "JWKS structure issues found" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "Invalid JSON response: $($_.Exception.Message)"; Details = @() }
    }
}

# Test 3: Base64 URL Encoding Validation
Test-JWKSEndpoint -TestName "Base64 URL Encoding Validation" -URL "$BaseURL/oauth2/jwks" -ValidationScript {
    param($response)
    
    try {
        $jwks = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        if ($jwks.keys -and $jwks.keys.Count -gt 0) {
            $key = $jwks.keys[0]
            
            # Test n parameter base64url decoding
            try {
                $nBytes = [System.Convert]::FromBase64String($key.n.Replace('-', '+').Replace('_', '/').PadRight($key.n.Length + (4 - $key.n.Length % 4) % 4, '='))
                $details += "✓ RSA modulus (n) base64url decoding: success ($($nBytes.Length) bytes)"
            } catch {
                $issues += "✗ RSA modulus (n) base64url decoding failed: $($_.Exception.Message)"
            }
            
            # Test e parameter base64url decoding
            try {
                $eBytes = [System.Convert]::FromBase64String($key.e.Replace('-', '+').Replace('_', '/').PadRight($key.e.Length + (4 - $key.e.Length % 4) % 4, '='))
                $details += "✓ RSA exponent (e) base64url decoding: success ($($eBytes.Length) bytes)"
                
                # Check if e is the standard 65537 (0x010001)
                if ($eBytes.Length -eq 3 -and $eBytes[0] -eq 1 -and $eBytes[1] -eq 0 -and $eBytes[2] -eq 1) {
                    $details += "✓ RSA exponent value: 65537 (standard)"
                } else {
                    $details += "⚠ RSA exponent value: non-standard"
                }
            } catch {
                $issues += "✗ RSA exponent (e) base64url decoding failed: $($_.Exception.Message)"
            }
        } else {
            $issues += "✗ No keys to validate"
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "Base64 URL encoding valid" } else { "Base64 URL encoding issues found" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "JSON parsing failed: $($_.Exception.Message)"; Details = @() }
    }
}

# Test 4: Apple Business Manager Compatibility
Test-JWKSEndpoint -TestName "Apple Business Manager Compatibility" -URL "$BaseURL/oauth2/jwks" -ValidationScript {
    param($response)
    
    try {
        $jwks = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        # Check response size (Apple has limits)
        $responseSize = $response.Content.Length
        if ($responseSize -lt 10000) { # 10KB limit
            $details += "✓ Response size: $responseSize bytes (within limits)"
        } else {
            $issues += "✗ Response size too large: $responseSize bytes"
        }
        
        # Check for minimal required fields only (Apple prefers simple JWKS)
        if ($jwks.keys -and $jwks.keys.Count -gt 0) {
            $key = $jwks.keys[0]
            
            # Count optional fields that might cause issues
            $optionalFields = @("x5t", "x5c", "key_ops")
            $presentOptionalFields = @()
            foreach ($field in $optionalFields) {
                if ($key.$field) {
                    $presentOptionalFields += $field
                }
            }
            
            if ($presentOptionalFields.Count -eq 0) {
                $details += "✓ No optional fields present (Apple compatible)"
            } else {
                $details += "⚠ Optional fields present: $($presentOptionalFields -join ', ') (may cause issues)"
            }
            
            # Check key ID format
            if ($key.kid -and $key.kid.Length -le 50) {
                $details += "✓ Key ID length: $($key.kid.Length) chars (acceptable)"
            } else {
                $issues += "✗ Key ID too long or missing"
            }
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "Apple Business Manager compatible" } else { "Apple Business Manager compatibility issues" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "Compatibility check failed: $($_.Exception.Message)"; Details = @() }
    }
}

# Output Results
Write-Host "`n📊 JWKS Endpoint Test Results" -ForegroundColor Magenta
Write-Host "Total Tests: $($Results.Total)" -ForegroundColor Cyan
Write-Host "Passed: $($Results.Passed)" -ForegroundColor Green
Write-Host "Failed: $($Results.Failed)" -ForegroundColor Red

$successRate = if ($Results.Total -gt 0) { 
    [math]::Round(($Results.Passed / $Results.Total) * 100, 2) 
} else { 0 }

Write-Host "Success Rate: $successRate%" -ForegroundColor $(
    if ($successRate -ge 95) { "Green" } 
    elseif ($successRate -ge 80) { "Yellow" } 
    else { "Red" }
)

# Final Assessment
Write-Host "`n🎯 Apple Business Manager Error Code -27480 Analysis" -ForegroundColor Magenta

if ($Results.Failed -eq 0) {
    Write-Host "🎉 EXCELLENT: JWKS endpoint should work with Apple Business Manager!" -ForegroundColor Green
    Write-Host "   ✅ All JWKS structure requirements met" -ForegroundColor Green
    Write-Host "   ✅ Base64 URL encoding correct" -ForegroundColor Green
    Write-Host "   ✅ Apple Business Manager compatibility confirmed" -ForegroundColor Green
} elseif ($successRate -ge 75) {
    Write-Host "⚠️  CAUTION: Minor issues found that might cause error -27480" -ForegroundColor Yellow
} else {
    Write-Host "❌ CRITICAL: Major issues found that likely cause error -27480" -ForegroundColor Red
}

Write-Host "`n💡 Error Code -27480 Common Causes:" -ForegroundColor Cyan
Write-Host "1. Invalid JWKS JSON structure" -ForegroundColor Gray
Write-Host "2. Incorrect Base64 URL encoding of RSA parameters" -ForegroundColor Gray
Write-Host "3. Missing required JWK fields (kty, use, kid, alg, n, e)" -ForegroundColor Gray
Write-Host "4. Wrong Content-Type header (must be application/json)" -ForegroundColor Gray
Write-Host "5. RSA key parameters not properly formatted" -ForegroundColor Gray

Write-Host "`n🔧 Recommended Actions:" -ForegroundColor Cyan
Write-Host "1. Ensure JWKS response is valid JSON" -ForegroundColor Gray
Write-Host "2. Verify RSA public key encoding is correct" -ForegroundColor Gray
Write-Host "3. Remove optional fields that might confuse Apple" -ForegroundColor Gray
Write-Host "4. Test with Apple Business Manager in sandbox mode" -ForegroundColor Gray

Write-Host "`n🔑 JWKS Endpoint Test Complete!" -ForegroundColor Green
