# Generated by Django 4.2.6 on 2023-10-12 15:26

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_rbac", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemPermission",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
            ],
            options={
                "permissions": [
                    ("view_system_info", "Can view system info"),
                    ("view_system_tasks", "Can view system tasks"),
                    ("run_system_tasks", "Can run system tasks"),
                    ("access_admin_interface", "Can access admin interface"),
                ],
                "verbose_name": "System permission",
                "verbose_name_plural": "System permissions",
                "managed": False,
                "default_permissions": (),
            },
        ),
    ]
