# Generated by Django 3.0.6 on 2020-05-19 22:07

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Policy",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "policy_uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.TextField(blank=True, null=True)),
                ("negate", models.BooleanField(default=False)),
                ("order", models.IntegerField(default=0)),
                ("timeout", models.IntegerField(default=30)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PolicyBinding",
            fields=[
                (
                    "policy_binding_uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("enabled", models.BooleanField(default=True)),
                ("order", models.IntegerField(default=0)),
                (
                    "policy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="authentik_policies.Policy",
                    ),
                ),
            ],
            options={
                "verbose_name": "Policy Binding",
                "verbose_name_plural": "Policy Bindings",
            },
        ),
        migrations.CreateModel(
            name="PolicyBindingModel",
            fields=[
                (
                    "pbm_uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "policies",
                    models.ManyToManyField(
                        blank=True,
                        related_name="bindings",
                        through="authentik_policies.PolicyBinding",
                        to="authentik_policies.Policy",
                    ),
                ),
            ],
            options={
                "verbose_name": "Policy Binding Model",
                "verbose_name_plural": "Policy Binding Models",
            },
        ),
        migrations.AddField(
            model_name="policybinding",
            name="target",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="authentik_policies.PolicyBindingModel",
            ),
        ),
    ]
