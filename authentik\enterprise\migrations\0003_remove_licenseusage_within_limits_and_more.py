# Generated by Django 5.0.8 on 2024-08-08 14:15

from django.db import migrations, models
from django.apps.registry import Apps
from django.db.backends.base.schema import BaseDatabaseSchemaEditor


def migrate_license_usage(apps: Apps, schema_editor: BaseDatabaseSchemaEditor):
    LicenseUsage = apps.get_model("authentik_enterprise", "licenseusage")
    db_alias = schema_editor.connection.alias

    for usage in LicenseUsage.objects.using(db_alias).all():
        usage.status = "valid" if usage.within_limits else "limit_exceeded_admin"
        usage.save()


class Migration(migrations.Migration):

    dependencies = [
        ("authentik_enterprise", "0002_rename_users_license_internal_users_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="licenseusage",
            name="status",
            field=models.TextField(
                choices=[
                    ("unlicensed", "Unlicensed"),
                    ("valid", "Valid"),
                    ("expired", "Expired"),
                    ("expiry_soon", "Expiry Soon"),
                    ("limit_exceeded_admin", "Limit Exceeded Admin"),
                    ("limit_exceeded_user", "Limit Exceeded User"),
                    ("read_only", "Read Only"),
                ],
                default=None,
                null=True,
            ),
            preserve_default=False,
        ),
        migrations.RunPython(migrate_license_usage),
        migrations.RemoveField(
            model_name="licenseusage",
            name="within_limits",
        ),
        migrations.AlterField(
            model_name="licenseusage",
            name="status",
            field=models.TextField(
                choices=[
                    ("unlicensed", "Unlicensed"),
                    ("valid", "Valid"),
                    ("expired", "Expired"),
                    ("expiry_soon", "Expiry Soon"),
                    ("limit_exceeded_admin", "Limit Exceeded Admin"),
                    ("limit_exceeded_user", "Limit Exceeded User"),
                    ("read_only", "Read Only"),
                ],
            ),
            preserve_default=False,
        ),
        migrations.RenameField(
            model_name="licenseusage",
            old_name="user_count",
            new_name="internal_user_count",
        ),
    ]
