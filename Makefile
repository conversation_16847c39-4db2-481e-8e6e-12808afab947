.PHONY: init
init:
	go install github.com/google/wire/cmd/wire@latest
	go install github.com/swaggo/swag/cmd/swag@latest

.PHONY: bootstrap
bootstrap:
	cd ./deploy/docker-compose && docker compose up -d && cd ../../
	go run ./cmd/migration
	nunu run ./cmd/server



.PHONY: build
build:
	go build -ldflags="-s -w" -o ./bin/server ./cmd/server
	cd web && npm run build

.PHONY: build-linux
build-linux:
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -ldflags="-s -w" -o ./bin/go-abm-idp-linux-amd64 ./cmd/server

.PHONY: build-all
build-all:
	# Windows
	GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o ./bin/go-abm-idp-windows-amd64.exe ./cmd/server
	# Linux
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -ldflags="-s -w" -o ./bin/go-abm-idp-linux-amd64 ./cmd/server
	# macOS
	GOOS=darwin GOARCH=amd64 go build -ldflags="-s -w" -o ./bin/go-abm-idp-darwin-amd64 ./cmd/server

.PHONY: docker
docker:
	docker build -f deploy/build/Dockerfile --build-arg APP_RELATIVE_PATH=./cmd/task -t 1.1.1.1:5000/demo-task:v1 .
	docker run --rm -i 1.1.1.1:5000/demo-task:v1

.PHONY: swag
swag:
	swag init  -g cmd/server/main.go -o ./docs --parseDependency
