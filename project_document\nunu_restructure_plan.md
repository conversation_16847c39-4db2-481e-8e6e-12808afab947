# Go-ABM-IDP项目Nunu标准重构方案

## 项目概述
- **项目名称**: go-abm-idp (OAuth2/OIDC身份提供者)
- **当前状态**: 基本符合nunu结构，但需要优化和标准化
- **目标**: 完全符合nunu脚手架标准，保持OAuth2/OIDC功能完整性

## 当前结构分析

### ✅ 已符合nunu标准的目录
- `api/v1/` - API定义
- `cmd/` - 应用入口点（server、migration、task）
- `config/` - 配置文件
- `internal/handler/` - HTTP处理器
- `internal/service/` - 业务逻辑层
- `internal/repository/` - 数据访问层
- `internal/model/` - 数据模型
- `internal/middleware/` - 中间件
- `internal/server/` - 服务器配置
- `pkg/` - 公共代码
- `web/` - 前端代码
- `docs/` - 文档
- `deploy/` - 部署文件

### ❌ 需要调整的部分
1. **目录重命名**:
   - `脚本/` → `scripts/`
   - `文档/` → 整合到 `docs/zh/`

2. **缺失的标准目录**:
   - `test/` 目录结构不完整
   - 缺少标准的测试组织

3. **internal目录优化**:
   - 过多的专业化子目录需要重组
   - 部分功能可以合并

## 详细重构计划

### 阶段1: 基础目录调整

#### 1.1 重命名和移动目录
```bash
# 重命名脚本目录
脚本/ → scripts/

# 整合中文文档
文档/ → docs/zh/
```

#### 1.2 创建标准test目录结构
```
test/
├── mocks/          # Mock对象
├── integration/    # 集成测试
├── unit/          # 单元测试
└── fixtures/      # 测试数据
```

### 阶段2: Internal目录重组

#### 2.1 安全相关模块整合
```bash
# 创建统一的安全模块
internal/security/
├── ai/            # 从 internal/ai/ 移动
├── intelligence/  # 从 internal/intelligence/ 移动
└── threat/        # 从 internal/threat/ 移动
```

#### 2.2 OAuth2模块整合
```bash
# FAPI功能移入OAuth2模块
internal/oauth2/fapi/  # 从 internal/fapi/ 移动
```

#### 2.3 加密模块整合
```bash
# HSM功能移入加密模块
internal/crypto/hsm/   # 从 internal/hsm/ 移动
```

### 阶段3: 服务层优化

#### 3.1 服务文件整理
- 检查重复的服务实现
- 合并相似功能的服务
- 确保单一职责原则

#### 3.2 保留的专业模块
- `internal/oauth2/` - OAuth2核心逻辑
- `internal/crypto/` - 加密和密钥管理
- `internal/webauthn/` - WebAuthn支持
- `internal/admin/` - 管理功能
- `internal/multitenant/` - 多租户支持

## 重构后的目标结构

```
go-abm-idp/
├── api/v1/                    # API定义
├── cmd/                       # 应用入口
│   ├── server/
│   ├── migration/
│   └── task/
├── config/                    # 配置文件
├── deploy/                    # 部署文件
├── docs/                      # 文档
│   ├── en/                    # 英文文档
│   └── zh/                    # 中文文档（从文档/移动）
├── internal/                  # 主要业务代码
│   ├── handler/               # HTTP处理器
│   ├── service/               # 业务逻辑
│   ├── repository/            # 数据访问
│   ├── model/                 # 数据模型
│   ├── middleware/            # 中间件
│   ├── server/                # 服务器配置
│   ├── oauth2/                # OAuth2核心功能
│   │   └── fapi/              # FAPI支持
│   ├── crypto/                # 加密功能
│   │   └── hsm/               # HSM支持
│   ├── security/              # 安全功能
│   │   ├── ai/
│   │   ├── intelligence/
│   │   └── threat/
│   ├── admin/                 # 管理功能
│   ├── webauthn/              # WebAuthn支持
│   ├── multitenant/           # 多租户支持
│   ├── monitoring/            # 监控功能
│   └── utils/                 # 工具函数
├── pkg/                       # 公共代码
├── scripts/                   # 脚本文件（从脚本/移动）
├── test/                      # 测试代码
│   ├── mocks/
│   ├── integration/
│   ├── unit/
│   └── fixtures/
├── web/                       # 前端代码
└── storage/                   # 存储文件
```

## 执行步骤

### Step 1: 准备工作
1. 备份当前项目
2. 确保所有更改都已提交到版本控制

### Step 2: 目录重命名和创建
1. 重命名 `脚本/` 为 `scripts/`
2. 移动 `文档/` 内容到 `docs/zh/`
3. 创建标准 `test/` 目录结构

### Step 3: Internal目录重组
1. 创建新的目录结构
2. 移动文件到新位置
3. 更新所有import路径

### Step 4: 验证和测试
1. 更新所有Go文件的import语句
2. 更新Wire配置文件
3. 编译测试项目
4. 运行所有测试确保功能完整

## 风险评估

### 高风险项
- OAuth2/OIDC核心功能的import路径更新
- Wire依赖注入配置的更新
- 大量文件移动可能导致的路径错误

### 缓解措施
- 分阶段执行，每个阶段后进行编译测试
- 保持详细的变更记录
- 在每个重要步骤后创建备份点

## 预期收益

1. **标准化**: 完全符合nunu脚手架标准
2. **可维护性**: 更清晰的目录结构和职责分离
3. **可扩展性**: 标准化的结构便于后续功能扩展
4. **团队协作**: 符合Go社区最佳实践，便于团队协作
