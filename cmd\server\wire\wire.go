//go:build wireinject
// +build wireinject

package wire

import (
	"fmt"
	"go-abm-idp/internal/config"
	"go-abm-idp/internal/handler"
	"go-abm-idp/internal/job"
	"go-abm-idp/internal/oauth2"
	"go-abm-idp/internal/repository"
	"go-abm-idp/internal/server"
	"go-abm-idp/internal/service"
	"go-abm-idp/pkg/app"
	"go-abm-idp/pkg/jwt"
	"go-abm-idp/pkg/log"
	"go-abm-idp/pkg/server/http"
	"go-abm-idp/pkg/sid"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	//repository.NewRedis,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewUserRepository,
	repository.NewCasbinEnforcer,
	repository.NewAdminRepository,
	repository.NewOAuth2Repository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewUserService,
	service.NewAdminService,
	service.NewOAuth2AdminService,
	service.NewOIDCService,
	service.NewSSFService,
	service.NewSSFJWTService,
	// 新增的高级服
	service.NewOAuth2AuditLogger,
	service.NewSSFEventManager,
	service.NewSSFProviderService,
	// 新增的适配器服
	service.NewOAuth2AdapterService,
	service.NewOAuth2IntrospectionAdapter,
	service.NewOAuth2RevocationAdapter,
	service.NewOAuth2ProductionService,
	// 安全增强服务
	service.SecurityEnhancedServiceSet,
	// 性能优化服务
	service.PerformanceOptimizationServiceSet,
	// OIDC统一服务
	service.OIDCUnifiedServiceSet,
	// Apple Business Manager服务
	service.NewAppleUserSyncService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	handler.NewUserHandler,
	handler.NewAdminHandler,
	handler.NewLoginHandler,
	handler.NewOAuth2AdminHandler,
	handler.NewOAuth2StandardHandler,
	handler.NewOAuth2ConsentHandler,
	handler.NewOAuth2ProductionHandler,
	handler.NewSSFFixedHandler,
	handler.NewSSFStandardHandler,
	// 保留的高级处理器
	handler.NewSSFHandler,
	// Apple Business Manager处理器
	provideAppleBusinessManagerHandler,
)

var jobSet = wire.NewSet(
	job.NewJob,
	job.NewUserJob,
)
var serverSet = wire.NewSet(
	server.NewHTTPServer,
	server.NewJobServer,
)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,
	// task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, jobServer),
		app.WithName("demo-server"),
	)
}

// provideJWTManager 提供JWT管理
func provideJWTManager(v *viper.Viper) (*oauth2.JWTManager, error) {
	// 从配置中获取JWT设置
	issuer := v.GetString("jwt.issuer")
	if issuer == "" {
		issuer = "nunu-oauth2"
	}
	keyID := v.GetString("jwt.key_id")
	if keyID == "" {
		keyID = "default"
	}

	// 检查是否配置了RSA私钥
	rsaPrivateKeyPEM := v.GetString("jwt.rsa_private_key")
	if rsaPrivateKeyPEM != "" {
		// 使用配置的RSA私钥
		return oauth2.NewJWTManagerFromPEM(rsaPrivateKeyPEM, issuer, keyID)
	}

	// 如果没有配置RSA密钥,生成一个临时的RSA密钥对用于JWKS
	// 注意:在生产环境中应该使用持久化的RSA密钥
	rsaKey, err := oauth2.GenerateRSAKey(2048)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %w", err)
	}

	return oauth2.NewJWTManager(rsaKey, issuer, keyID)
}

// provideConfig 提供配置
func provideConfig(v *viper.Viper) *config.Config {
	// 🔧 Apple Business Manager修复：从viper配置中读取实际配置
	cfg := config.DefaultConfig()

	// 从viper中读取服务器配置
	if v.IsSet("server.base_url") {
		cfg.Server.BaseURL = v.GetString("server.base_url")
	}
	if v.IsSet("server.host") {
		cfg.Server.Host = v.GetString("server.host")
	}
	if v.IsSet("server.port") {
		cfg.Server.Port = v.GetInt("server.port")
	}
	if v.IsSet("server.mode") {
		cfg.Server.Mode = v.GetString("server.mode")
	}

	// 从viper中读取JWT配置
	if v.IsSet("jwt.issuer") {
		cfg.JWT.Issuer = v.GetString("jwt.issuer")
	}
	if v.IsSet("jwt.key_id") {
		cfg.JWT.KeyID = v.GetString("jwt.key_id")
	}

	return cfg
}

// provideZapLogger 提供zap.Logger
func provideZapLogger(logger *log.Logger) *zap.Logger {
	// 从现有的log.Logger获取zap.Logger
	return logger.Logger
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		jobSet,
		serverSet,
		sid.NewSid,
		jwt.NewJwt,
		provideConfig,
		provideJWTManager,
		provideZapLogger,
		newApp,
	))
}

// provideAppleBusinessManagerHandler 提供Apple Business Manager处理器
func provideAppleBusinessManagerHandler(logger *log.Logger) (*handler.AppleBusinessManagerHandler, error) {
	return handler.NewAppleBusinessManagerHandler(logger)
}
