# Generated by Django 5.0.6 on 2024-06-11 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentik_policies_reputation", "0006_reputation_ip_asn_data"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="reputation",
            index=models.Index(fields=["identifier"], name="authentik_p_identif_9434d7_idx"),
        ),
        migrations.AddIndex(
            model_name="reputation",
            index=models.Index(fields=["ip"], name="authentik_p_ip_7ad0df_idx"),
        ),
        migrations.AddIndex(
            model_name="reputation",
            index=models.Index(fields=["ip", "identifier"], name="authentik_p_ip_d779aa_idx"),
        ),
    ]
