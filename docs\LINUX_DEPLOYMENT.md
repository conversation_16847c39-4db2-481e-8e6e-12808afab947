# Linux部署指南 - go-abm-idp

## 📋 概述

本指南详细说明如何在Linux服务器上部署go-abm-idp (Apple Business Manager Identity Provider)。

## 🎯 系统要求

### 最低要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+ / Debian 11+
- **架构**: x86_64 (amd64)
- **内存**: 512MB RAM (推荐 1GB+)
- **存储**: 1GB 可用空间
- **网络**: 443端口 (HTTPS) 和 8000端口 (HTTP) 访问权限

### 依赖软件
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **反向代理**: Nginx 或 Apache (可选，推荐)
- **SSL证书**: 有效的SSL/TLS证书 (Apple Business Manager要求HTTPS)

## 🚀 快速部署

### 步骤1: 下载和准备

```bash
# 创建部署目录
mkdir -p /opt/go-abm-idp
cd /opt/go-abm-idp

# 复制Linux二进制文件 (从编译机器)
# 使用以下文件之一:
# - bin/go-abm-idp-linux-amd64
# - bin/go-abm-idp-linux-amd64-latest
# - bin/go-abm-idp-linux-amd64-new

# 设置执行权限
chmod +x go-abm-idp-linux-amd64
```

### 步骤2: 自动部署 (推荐)

```bash
# 复制部署脚本
cp scripts/deploy-linux.sh /tmp/
chmod +x /tmp/deploy-linux.sh

# 运行自动部署
sudo /tmp/deploy-linux.sh
```

### 步骤3: 手动部署 (高级用户)

#### 3.1 创建用户和目录

```bash
# 创建系统用户
sudo useradd --system --shell /bin/false --home-dir /opt/go-abm-idp --create-home abm-idp

# 创建必要目录
sudo mkdir -p /opt/go-abm-idp/{bin,config,storage,logs}
sudo mkdir -p /etc/go-abm-idp
sudo mkdir -p /var/log/go-abm-idp

# 设置权限
sudo chown -R abm-idp:abm-idp /opt/go-abm-idp
sudo chown -R abm-idp:abm-idp /var/log/go-abm-idp
```

#### 3.2 安装二进制文件

```bash
# 复制二进制文件
sudo cp go-abm-idp-linux-amd64 /opt/go-abm-idp/bin/go-abm-idp
sudo chmod +x /opt/go-abm-idp/bin/go-abm-idp
sudo chown abm-idp:abm-idp /opt/go-abm-idp/bin/go-abm-idp
```

#### 3.3 配置文件

```bash
# 创建生产配置
sudo tee /etc/go-abm-idp/app.production.yml > /dev/null << 'EOF'
app:
  name: "go-abm-idp"
  version: "1.0.0"
  env: "production"
  debug: false

http:
  host: "0.0.0.0"
  port: 8000
  tls:
    enabled: true
    cert_file: "/opt/go-abm-idp/ssl/server.crt"
    key_file: "/opt/go-abm-idp/ssl/server.key"

database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  database: "go_abm_idp"
  username: "abm_idp"
  password: "your_secure_password_here"

jwt:
  secret: "your_jwt_secret_key_here_minimum_32_characters"
  expire: 3600

apple_business_manager:
  issuer: "https://your-domain.com/application/o/abm"
  client_id: "apple-business-manager"
  client_secret: "your_apple_client_secret"

log:
  level: "info"
  format: "json"
  output: "/var/log/go-abm-idp/app.log"
EOF

# 设置配置文件权限
sudo chown root:abm-idp /etc/go-abm-idp/app.production.yml
sudo chmod 640 /etc/go-abm-idp/app.production.yml
```

#### 3.4 创建Systemd服务

```bash
# 创建服务文件
sudo tee /etc/systemd/system/go-abm-idp.service > /dev/null << 'EOF'
[Unit]
Description=Apple Business Manager Identity Provider
Documentation=https://github.com/your-org/go-abm-idp
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=abm-idp
Group=abm-idp
WorkingDirectory=/opt/go-abm-idp
ExecStart=/opt/go-abm-idp/bin/go-abm-idp
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=go-abm-idp

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/go-abm-idp /var/log/go-abm-idp
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Environment
Environment=GIN_MODE=release
Environment=CONFIG_PATH=/etc/go-abm-idp/app.production.yml

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload
```

## 🔧 配置

### 数据库设置

#### MySQL
```sql
-- 创建数据库和用户
CREATE DATABASE go_abm_idp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'abm_idp'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON go_abm_idp.* TO 'abm_idp'@'localhost';
FLUSH PRIVILEGES;
```

#### PostgreSQL
```sql
-- 创建数据库和用户
CREATE DATABASE go_abm_idp;
CREATE USER abm_idp WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE go_abm_idp TO abm_idp;
```

### SSL证书配置

#### 使用Let's Encrypt (推荐)
```bash
# 安装certbot
sudo apt update
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到应用目录
sudo mkdir -p /opt/go-abm-idp/ssl
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/go-abm-idp/ssl/server.crt
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/go-abm-idp/ssl/server.key
sudo chown -R abm-idp:abm-idp /opt/go-abm-idp/ssl
sudo chmod 600 /opt/go-abm-idp/ssl/server.key
```

#### 使用自签名证书 (仅测试)
```bash
# 生成自签名证书
sudo mkdir -p /opt/go-abm-idp/ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /opt/go-abm-idp/ssl/server.key \
    -out /opt/go-abm-idp/ssl/server.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=your-domain.com"

sudo chown -R abm-idp:abm-idp /opt/go-abm-idp/ssl
sudo chmod 600 /opt/go-abm-idp/ssl/server.key
```

## 🎮 服务管理

### 启动服务
```bash
# 启动服务
sudo systemctl start go-abm-idp

# 设置开机自启
sudo systemctl enable go-abm-idp

# 检查状态
sudo systemctl status go-abm-idp
```

### 查看日志
```bash
# 实时查看日志
sudo journalctl -u go-abm-idp -f

# 查看最近日志
sudo journalctl -u go-abm-idp -n 100

# 查看应用日志文件
sudo tail -f /var/log/go-abm-idp/app.log
```

### 重启和停止
```bash
# 重启服务
sudo systemctl restart go-abm-idp

# 停止服务
sudo systemctl stop go-abm-idp

# 重新加载配置
sudo systemctl reload go-abm-idp
```

## 🔍 验证部署

### 健康检查
```bash
# 检查服务状态
curl -k https://localhost:8000/.well-known/openid_configuration

# 检查JWKS端点
curl -k https://localhost:8000/oauth2/jwks

# 检查SSF配置
curl -k https://localhost:8000/.well-known/ssf_configuration
```

### Apple Business Manager测试
```bash
# 运行诊断脚本 (如果可用)
./scripts/apple-diagnostic-simple.ps1 -BaseURL "https://your-domain.com"
```

## 🔒 安全建议

1. **防火墙配置**
   ```bash
   # UFW
   sudo ufw allow 22/tcp    # SSH
   sudo ufw allow 80/tcp    # HTTP (重定向到HTTPS)
   sudo ufw allow 443/tcp   # HTTPS
   sudo ufw enable
   
   # iptables
   sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
   sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
   sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
   ```

2. **定期更新**
   - 定期更新操作系统和依赖
   - 监控安全补丁
   - 更新SSL证书

3. **监控和日志**
   - 设置日志轮转
   - 监控服务状态
   - 设置告警

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查配置文件语法
   /opt/go-abm-idp/bin/go-abm-idp --config-check
   
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   
   # 检查权限
   sudo ls -la /opt/go-abm-idp/bin/go-abm-idp
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   mysql -h localhost -u abm_idp -p go_abm_idp
   
   # 检查数据库服务
   sudo systemctl status mysql
   ```

3. **SSL证书问题**
   ```bash
   # 验证证书
   openssl x509 -in /opt/go-abm-idp/ssl/server.crt -text -noout
   
   # 检查证书权限
   sudo ls -la /opt/go-abm-idp/ssl/
   ```

## 📞 支持

如果遇到问题，请检查：
1. 系统日志: `sudo journalctl -u go-abm-idp`
2. 应用日志: `/var/log/go-abm-idp/app.log`
3. 配置文件: `/etc/go-abm-idp/app.production.yml`

## 🎉 完成

部署完成后，您的Apple Business Manager Identity Provider将在以下地址可用：

- **OIDC Discovery**: `https://your-domain.com/.well-known/openid_configuration`
- **JWKS Endpoint**: `https://your-domain.com/oauth2/jwks`
- **SSF Configuration**: `https://your-domain.com/.well-known/ssf_configuration`
