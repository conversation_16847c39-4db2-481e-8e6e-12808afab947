# Standards Compliance Test for Apple Business Manager Custom Identity Provider

param(
    [string]$BaseURL = "http://localhost:8000"
)

Write-Host "🔍 Standards Compliance Test" -ForegroundColor Green
Write-Host "Target URL: $BaseURL" -ForegroundColor Cyan
Write-Host "Test Time: $(Get-Date)" -ForegroundColor Gray

$Results = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-StandardCompliance {
    param(
        [string]$TestName,
        [string]$URL,
        [scriptblock]$ValidationScript
    )
    
    $Results.Total++
    Write-Host "`n🔍 Testing: $TestName" -ForegroundColor Yellow
    Write-Host "   URL: $URL" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $URL -UseBasicParsing -ErrorAction Stop
        $result = & $ValidationScript $response
        
        if ($result.Success) {
            Write-Host "   ✅ PASS: $($result.Message)" -ForegroundColor Green
            $Results.Passed++
        } else {
            Write-Host "   ❌ FAIL: $($result.Message)" -ForegroundColor Red
            $Results.Failed++
        }
        
        if ($result.Details) {
            foreach ($detail in $result.Details) {
                Write-Host "     - $detail" -ForegroundColor Gray
            }
        }
        
    } catch {
        Write-Host "   ❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        $Results.Failed++
    }
}

# Test 1: OpenID Connect Discovery 1.0 Compliance
Test-StandardCompliance -TestName "OpenID Connect Discovery 1.0 Compliance" -URL "$BaseURL/.well-known/openid_configuration" -ValidationScript {
    param($response)
    
    try {
        $config = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        # Required fields per OpenID Connect Discovery 1.0
        $requiredFields = @(
            "issuer",
            "authorization_endpoint",
            "token_endpoint",
            "jwks_uri",
            "response_types_supported",
            "subject_types_supported",
            "id_token_signing_alg_values_supported"
        )
        
        foreach ($field in $requiredFields) {
            if ($config.$field) {
                $details += "✓ $field: present"
            } else {
                $issues += "✗ $field: missing"
            }
        }
        
        # Check recommended fields
        $recommendedFields = @(
            "userinfo_endpoint",
            "scopes_supported",
            "claims_supported",
            "grant_types_supported"
        )
        
        foreach ($field in $recommendedFields) {
            if ($config.$field) {
                $details += "✓ $field: present (recommended)"
            } else {
                $details += "⚠ $field: missing (recommended)"
            }
        }
        
        # Check API version information
        if ($config.api_version) {
            $details += "✓ API Version: $($config.api_version)"
        }
        
        if ($config.openid_connect_version) {
            $details += "✓ OpenID Connect Version: $($config.openid_connect_version)"
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "All required OIDC Discovery fields present" } else { "Missing required fields: $($issues -join ', ')" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "Invalid JSON response"; Details = @() }
    }
}

# Test 2: Security Headers Compliance
Test-StandardCompliance -TestName "Security Headers Compliance" -URL "$BaseURL/.well-known/openid_configuration" -ValidationScript {
    param($response)
    
    $details = @()
    $issues = @()
    
    # Required security headers
    $securityHeaders = @{
        "X-Content-Type-Options" = "nosniff"
        "X-Frame-Options" = "DENY"
        "X-XSS-Protection" = "1; mode=block"
        "Content-Security-Policy" = $null
        "Referrer-Policy" = $null
    }
    
    foreach ($header in $securityHeaders.Keys) {
        $headerValue = $response.Headers[$header]
        if ($headerValue) {
            $details += "✓ $header: $headerValue"
            if ($securityHeaders[$header] -and $headerValue -ne $securityHeaders[$header]) {
                $issues += "⚠ $header: unexpected value"
            }
        } else {
            $issues += "✗ $header: missing"
        }
    }
    
    # Check Content-Type
    $contentType = $response.Headers["Content-Type"]
    if ($contentType -and $contentType.StartsWith("application/json")) {
        $details += "✓ Content-Type: $contentType"
    } else {
        $issues += "✗ Content-Type: not application/json"
    }
    
    $success = $issues.Count -eq 0
    $message = if ($success) { "All security headers properly configured" } else { "Security header issues found" }
    
    return @{ Success = $success; Message = $message; Details = $details }
}

# Test 3: SSF Configuration Compliance (RFC 8935)
Test-StandardCompliance -TestName "SSF Configuration Compliance (RFC 8935)" -URL "$BaseURL/.well-known/ssf_configuration" -ValidationScript {
    param($response)
    
    try {
        $config = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        # Required SSF fields
        $requiredFields = @(
            "issuer",
            "jwks_uri",
            "delivery_methods_supported",
            "events_supported"
        )
        
        foreach ($field in $requiredFields) {
            if ($config.$field) {
                $details += "✓ $field: present"
            } else {
                $issues += "✗ $field: missing"
            }
        }
        
        # Check delivery methods
        if ($config.delivery_methods_supported) {
            $supportedMethods = $config.delivery_methods_supported
            if ($supportedMethods -contains "https://schemas.openid.net/secevent/risc/delivery-method/push") {
                $details += "✓ Push delivery method supported"
            }
            if ($supportedMethods -contains "https://schemas.openid.net/secevent/risc/delivery-method/poll") {
                $details += "✓ Poll delivery method supported"
            }
        }
        
        # Check SET token configuration
        if ($config.set_token_signing_alg_values_supported) {
            $details += "✓ SET Token Signing Algorithms: $($config.set_token_signing_alg_values_supported -join ', ')"
        }
        
        if ($config.set_token_encryption_alg_values_supported) {
            $details += "✓ SET Token Encryption Algorithms: $($config.set_token_encryption_alg_values_supported -join ', ')"
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "SSF configuration compliant with RFC 8935" } else { "SSF compliance issues found" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "Invalid JSON response"; Details = @() }
    }
}

# Test 4: JWKS Endpoint Compliance (RFC 7517)
Test-StandardCompliance -TestName "JWKS Endpoint Compliance (RFC 7517)" -URL "$BaseURL/oauth2/jwks" -ValidationScript {
    param($response)
    
    try {
        $jwks = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        # Check JWKS structure
        if ($jwks.keys) {
            $details += "✓ JWKS keys array present"
            
            if ($jwks.keys.Count -gt 0) {
                $key = $jwks.keys[0]
                
                # Required JWK fields
                $requiredKeyFields = @("kty", "use", "kid", "alg")
                foreach ($field in $requiredKeyFields) {
                    if ($key.$field) {
                        $details += "✓ Key $field: $($key.$field)"
                    } else {
                        $issues += "✗ Key $field: missing"
                    }
                }
                
                # Check RSA specific fields
                if ($key.kty -eq "RSA") {
                    if ($key.n -and $key.e) {
                        $details += "✓ RSA public key components (n, e) present"
                    } else {
                        $issues += "✗ RSA public key components missing"
                    }
                }
                
            } else {
                $issues += "✗ No keys in JWKS"
            }
        } else {
            $issues += "✗ JWKS keys array missing"
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "JWKS compliant with RFC 7517" } else { "JWKS compliance issues found" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "Invalid JSON response"; Details = @() }
    }
}

# Test 5: Apple Business Manager Specific Configuration
Test-StandardCompliance -TestName "Apple Business Manager Configuration" -URL "$BaseURL/.well-known/apple_business_manager_configuration" -ValidationScript {
    param($response)
    
    try {
        $config = $response.Content | ConvertFrom-Json
        $details = @()
        $issues = @()
        
        # Check Apple Business Manager specific fields
        if ($config.user_management) {
            $details += "✓ User management configuration present"
            
            $userMgmt = $config.user_management
            if ($userMgmt.supported_sync_types) {
                $details += "✓ Supported sync types: $($userMgmt.supported_sync_types -join ', ')"
            }
            
            if ($userMgmt.supported_permissions) {
                $details += "✓ Supported permissions: $($userMgmt.supported_permissions.Count) permissions"
            }
            
            if ($userMgmt.sync_limits) {
                $details += "✓ Sync limits configured"
            }
        } else {
            $issues += "✗ User management configuration missing"
        }
        
        if ($config.ssf_events) {
            $details += "✓ SSF events configuration present"
            
            $ssfEvents = $config.ssf_events
            if ($ssfEvents.supported_events) {
                $details += "✓ Supported SSF events: $($ssfEvents.supported_events -join ', ')"
            }
        } else {
            $issues += "✗ SSF events configuration missing"
        }
        
        if ($config.security) {
            $details += "✓ Security configuration present"
            
            $security = $config.security
            if ($security.pkce_required) {
                $details += "✓ PKCE required: $($security.pkce_required)"
            }
            
            if ($security.token_signing_algorithm) {
                $details += "✓ Token signing algorithm: $($security.token_signing_algorithm)"
            }
        } else {
            $issues += "✗ Security configuration missing"
        }
        
        $success = $issues.Count -eq 0
        $message = if ($success) { "Apple Business Manager configuration complete" } else { "Apple Business Manager configuration issues found" }
        
        return @{ Success = $success; Message = $message; Details = $details }
        
    } catch {
        return @{ Success = $false; Message = "Invalid JSON response"; Details = @() }
    }
}

# Output Results
Write-Host "`n📊 Standards Compliance Test Results" -ForegroundColor Magenta
Write-Host "Total Tests: $($Results.Total)" -ForegroundColor Cyan
Write-Host "Passed: $($Results.Passed)" -ForegroundColor Green
Write-Host "Failed: $($Results.Failed)" -ForegroundColor Red

$complianceScore = if ($Results.Total -gt 0) { 
    [math]::Round(($Results.Passed / $Results.Total) * 100, 2) 
} else { 0 }

Write-Host "Compliance Score: $complianceScore%" -ForegroundColor $(
    if ($complianceScore -ge 95) { "Green" } 
    elseif ($complianceScore -ge 80) { "Yellow" } 
    else { "Red" }
)

# Final Assessment
Write-Host "`n🎯 Standards Compliance Assessment" -ForegroundColor Magenta

if ($Results.Failed -eq 0) {
    Write-Host "🎉 EXCELLENT: Full compliance with all tested standards!" -ForegroundColor Green
    Write-Host "   ✅ OpenID Connect Discovery 1.0" -ForegroundColor Green
    Write-Host "   ✅ Security Headers Best Practices" -ForegroundColor Green
    Write-Host "   ✅ SSF Configuration (RFC 8935)" -ForegroundColor Green
    Write-Host "   ✅ JWKS Endpoint (RFC 7517)" -ForegroundColor Green
    Write-Host "   ✅ Apple Business Manager Requirements" -ForegroundColor Green
} elseif ($complianceScore -ge 80) {
    Write-Host "✅ GOOD: High compliance with minor issues to address." -ForegroundColor Yellow
} else {
    Write-Host "⚠️  ATTENTION: Compliance issues found that should be addressed." -ForegroundColor Red
}

Write-Host "`n📋 Standards Tested:" -ForegroundColor Cyan
Write-Host "• OpenID Connect Discovery 1.0 (RFC 8414)" -ForegroundColor Gray
Write-Host "• OAuth 2.0 Authorization Framework (RFC 6749)" -ForegroundColor Gray
Write-Host "• JSON Web Key Set (RFC 7517)" -ForegroundColor Gray
Write-Host "• Security Event Token (RFC 8935)" -ForegroundColor Gray
Write-Host "• HTTP Security Headers (OWASP)" -ForegroundColor Gray
Write-Host "• Apple Business Manager API Requirements" -ForegroundColor Gray

Write-Host "`n🔍 Standards Compliance Test Complete!" -ForegroundColor Green
