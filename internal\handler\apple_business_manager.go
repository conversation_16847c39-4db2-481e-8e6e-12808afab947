package handler

import (
	"crypto/rand"
	"crypto/rsa"
	"encoding/base64"
	"fmt"
	"go-abm-idp/pkg/log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

// AppleBusinessManagerHandler Apple Business Manager专用处理器
type AppleBusinessManagerHandler struct {
	logger       *log.Logger
	privateKey   *rsa.PrivateKey
	publicKey    *rsa.PublicKey
	jwtSecret    string
	issuer       string
	clientID     string
	clientSecret string
}

// NewAppleBusinessManagerHandler 创建Apple Business Manager处理器
func NewAppleBusinessManagerHandler(logger *log.Logger) (*AppleBusinessManagerHandler, error) {
	// 生成RSA密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %w", err)
	}

	return &AppleBusinessManagerHandler{
		logger:       logger,
		privateKey:   privateKey,
		publicKey:    &privateKey.PublicKey,
		jwtSecret:    "apple-business-manager-jwt-secret-2024-secure", // 在生产环境中从配置读取
		issuer:       "https://openid.akapril.in/application/o/abm",   // 匹配Authentik标准格式
		clientID:     "go-abm-idp-client",
		clientSecret: "ABM-GoIDP-2024-SecureSecret-Production",
	}, nil
}

// AppleTokenRequest Apple令牌请求
type AppleTokenRequest struct {
	GrantType    string `form:"grant_type" json:"grant_type"`
	Code         string `form:"code" json:"code"`
	RedirectURI  string `form:"redirect_uri" json:"redirect_uri"`
	ClientID     string `form:"client_id" json:"client_id"`
	ClientSecret string `form:"client_secret" json:"client_secret"`
	CodeVerifier string `form:"code_verifier" json:"code_verifier"`
}

// AppleTokenResponse Apple令牌响应
type AppleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token,omitempty"`
	IDToken      string `json:"id_token,omitempty"`
	Scope        string `json:"scope,omitempty"`
}

// AppleUserInfoResponse Apple用户信息响应
type AppleUserInfoResponse struct {
	Sub               string `json:"sub"`
	Name              string `json:"name,omitempty"`
	GivenName         string `json:"given_name,omitempty"`
	FamilyName        string `json:"family_name,omitempty"`
	Email             string `json:"email,omitempty"`
	EmailVerified     bool   `json:"email_verified,omitempty"`
	PreferredUsername string `json:"preferred_username,omitempty"`
}

// HandleAppleToken 处理Apple Business Manager令牌请求
func (h *AppleBusinessManagerHandler) HandleAppleToken(c *gin.Context) {
	// 记录详细的请求信息
	clientIP := h.getClientIP(c)
	userAgent := c.GetHeader("User-Agent")
	cfRay := c.GetHeader("CF-Ray")

	h.logger.Info("Apple Business Manager token request received",
		zap.String("ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.String("cf_ray", cfRay),
		zap.String("method", c.Request.Method),
		zap.String("content_type", c.GetHeader("Content-Type")),
	)

	var req AppleTokenRequest

	// 解析请求
	if err := c.ShouldBind(&req); err != nil {
		h.logger.Error("Token request parsing failed", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid request parameters",
		})
		return
	}

	// 记录请求参数（敏感信息除外）
	h.logger.Debug("Token request parameters",
		zap.String("grant_type", req.GrantType),
		zap.String("client_id", req.ClientID),
		zap.Bool("has_code", req.Code != ""),
		zap.Bool("has_redirect_uri", req.RedirectURI != ""),
		zap.Bool("has_code_verifier", req.CodeVerifier != ""),
	)

	// 验证grant_type
	if req.GrantType != "authorization_code" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "unsupported_grant_type",
			"error_description": "Only authorization_code grant type is supported",
		})
		return
	}

	// 记录客户端ID但不严格验证（Apple可能使用不同的client_id）
	h.logger.Info("Token request from client",
		zap.String("client_id", req.ClientID),
		zap.String("expected_client_id", h.clientID),
	)

	// 验证授权码（在实际实现中，这里应该验证真实的授权码）
	if req.Code == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_grant",
			"error_description": "Invalid authorization code",
		})
		return
	}

	// 生成访问令牌
	accessToken, err := h.generateAccessToken("apple-user-123")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate access token",
		})
		return
	}

	// 生成ID令牌（Apple Business Manager需要）
	idToken, err := h.generateIDToken("apple-user-123", req.ClientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate ID token",
		})
		return
	}

	// 生成刷新令牌
	refreshToken := h.generateRefreshToken()

	// 返回令牌响应 - 包含Apple请求的所有scope，永不过期
	response := AppleTokenResponse{
		AccessToken:  accessToken,
		TokenType:    "Bearer",
		ExpiresIn:    3153600000, // 100年的秒数，实际上永不过期
		RefreshToken: refreshToken,
		IDToken:      idToken,
		Scope:        "openid profile email ssf.manage ssf.read offline_access",
	}

	// 记录响应详情
	h.logger.Info("Token response generated successfully",
		zap.Int("access_token_length", len(accessToken)),
		zap.Int("id_token_length", len(idToken)),
		zap.Int("refresh_token_length", len(refreshToken)),
		zap.String("scope", response.Scope),
	)

	// 设置Apple Business Manager要求的响应头
	c.Header("Cache-Control", "no-store")
	c.Header("Pragma", "no-cache")
	c.JSON(http.StatusOK, response)
}

// HandleAppleUserInfo 处理Apple Business Manager用户信息请求
func (h *AppleBusinessManagerHandler) HandleAppleUserInfo(c *gin.Context) {
	// 获取Authorization头
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Missing access token",
		})
		return
	}

	// 验证Bearer令牌格式
	if !strings.HasPrefix(authHeader, "Bearer ") {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Invalid token format",
		})
		return
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")

	// 验证访问令牌
	userID, err := h.validateAccessToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Invalid or expired access token",
		})
		return
	}

	// 返回用户信息（根据实际用户数据调整）
	userInfo := AppleUserInfoResponse{
		Sub:               userID,
		Name:              "Apple Business Manager User",
		GivenName:         "Apple",
		FamilyName:        "User",
		Email:             "<EMAIL>",
		EmailVerified:     true,
		PreferredUsername: "appleuser",
	}

	c.JSON(http.StatusOK, userInfo)
}

// generateAccessToken 生成访问令牌 (RS256) - 永不过期
func (h *AppleBusinessManagerHandler) generateAccessToken(userID string) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"sub":   userID,
		"iss":   h.issuer,
		"aud":   []string{h.clientID, h.issuer},
		"exp":   now.Add(100 * 365 * 24 * time.Hour).Unix(), // 100年后过期，实际上永不过期
		"iat":   now.Unix(),
		"nbf":   now.Unix(),
		"jti":   h.generateJTI(),
		"scope": "openid profile email ssf.manage ssf.read offline_access",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = "apple-key-1"
	return token.SignedString(h.privateKey)
}

// generateIDToken 生成ID令牌 (RS256) - 永不过期
func (h *AppleBusinessManagerHandler) generateIDToken(userID, audience string) (string, error) {
	if audience == "" {
		audience = h.clientID
	}

	now := time.Now()
	claims := jwt.MapClaims{
		"sub":       userID,
		"iss":       h.issuer,
		"aud":       audience,
		"exp":       now.Add(100 * 365 * 24 * time.Hour).Unix(), // 100年后过期，实际上永不过期
		"iat":       now.Unix(),
		"nbf":       now.Unix(),
		"auth_time": now.Unix(),
		"jti":       h.generateJTI(),
		// Apple Business Manager需要的用户信息
		"name":               "Apple Business Manager User",
		"given_name":         "Apple",
		"family_name":        "User",
		"email":              "<EMAIL>",
		"email_verified":     true,
		"preferred_username": "appleuser",
		"updated_at":         now.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = "apple-key-1"
	return token.SignedString(h.privateKey)
}

// generateRefreshToken 生成刷新令牌
func (h *AppleBusinessManagerHandler) generateRefreshToken() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// generateJTI 生成JWT ID
func (h *AppleBusinessManagerHandler) generateJTI() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// generateNonce 生成nonce
func (h *AppleBusinessManagerHandler) generateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// validateAccessToken 验证访问令牌 (RS256)
func (h *AppleBusinessManagerHandler) validateAccessToken(tokenString string) (string, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return h.publicKey, nil
	})

	if err != nil {
		return "", err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		if sub, ok := claims["sub"].(string); ok {
			return sub, nil
		}
	}

	return "", fmt.Errorf("invalid token claims")
}

// HandleAppleWellKnown 处理Apple Business Manager的OIDC配置发现
// 基于Authentik ProviderInfoView实现模式
func (h *AppleBusinessManagerHandler) HandleAppleWellKnown(c *gin.Context) {
	// 动态构建issuer基于请求
	baseURL := fmt.Sprintf("https://%s", c.Request.Host)
	issuer := fmt.Sprintf("%s/application/o/abm", baseURL)

	config := map[string]interface{}{
		// 标准OIDC字段
		"issuer":                 issuer,
		"authorization_endpoint": fmt.Sprintf("%s/oauth2/authorize", baseURL),
		"token_endpoint":         fmt.Sprintf("%s/oauth2/token", baseURL),
		"userinfo_endpoint":      fmt.Sprintf("%s/oauth2/userinfo", baseURL),
		"jwks_uri":               fmt.Sprintf("%s/oauth2/jwks", baseURL),

		// API版本信息
		"api_version":            "1.0",
		"openid_connect_version": "1.0",
		"oauth2_version":         "2.0",
		"scopes_supported": []string{
			"openid",
			"profile",
			"email",
			"offline_access",
			"address",
			"phone",
			"ssf.manage",
			"ssf.read",
		},
		"response_types_supported": []string{
			"code",
			"id_token",
			"code id_token",
		},
		"grant_types_supported": []string{
			"authorization_code",
			"refresh_token",
			"client_credentials",
		},
		"subject_types_supported": []string{
			"public",
		},
		"id_token_signing_alg_values_supported": []string{
			"RS256",
		},
		"token_endpoint_auth_methods_supported": []string{
			"client_secret_post",
			"client_secret_basic",
		},
		"code_challenge_methods_supported": []string{
			"S256",
			"plain",
		},
		"token_endpoint_auth_signing_alg_values_supported": []string{
			"RS256",
		},
		"request_object_signing_alg_values_supported": []string{
			"RS256",
		},
		"claims_supported": []string{
			"sub",
			"iss",
			"aud",
			"exp",
			"iat",
			"auth_time",
			"nonce",
			"at_hash",
			"c_hash",
			"name",
			"given_name",
			"family_name",
			"email",
			"email_verified",
			"preferred_username",
			"profile",
			"locale",
			"updated_at",
		},
		// 标准OIDC扩展字段
		"userinfo_signing_alg_values_supported": []string{
			"RS256",
		},
		"display_values_supported": []string{
			"page",
			"popup",
		},
		"claim_types_supported": []string{
			"normal",
		},
		"service_documentation": fmt.Sprintf("%s/docs/oidc", baseURL),
		"op_policy_uri":         fmt.Sprintf("%s/privacy-policy", baseURL),
		"op_tos_uri":            fmt.Sprintf("%s/terms-of-service", baseURL),

		// Apple Business Manager特定配置
		"request_parameter_supported":      false,
		"request_uri_parameter_supported":  false,
		"require_request_uri_registration": false,

		// Apple Business Manager专用端点
		"apple_business_manager": map[string]interface{}{
			"user_sync_endpoint":         fmt.Sprintf("%s/application/o/abm/users/sync", baseURL),
			"password_change_endpoint":   fmt.Sprintf("%s/application/o/abm/users/password-changed", baseURL),
			"user_status_endpoint":       fmt.Sprintf("%s/application/o/abm/users/{user_id}/status", baseURL),
			"batch_user_status_endpoint": fmt.Sprintf("%s/application/o/abm/users/status", baseURL),
			"supported_sync_types":       []string{"full", "incremental", "delta"},
			"supported_permissions": []string{
				"read_users",
				"read_user_profiles",
				"read_user_status",
				"read_user_roles",
				"read_login_history",
				"read_password_status",
			},
			"max_users_per_sync":         1000,
			"max_users_per_batch_status": 100,
		},

		// 端点访问方法说明 (用于调试和文档)
		"_endpoint_methods": map[string]interface{}{
			"authorization_endpoint": []string{"GET", "POST"},
			"token_endpoint":         []string{"POST"},
			"userinfo_endpoint":      []string{"GET", "POST"},
			"jwks_uri":               []string{"GET"},
			"revocation_endpoint":    fmt.Sprintf("%s/oauth2/revoke", baseURL),
			"introspection_endpoint": fmt.Sprintf("%s/oauth2/introspect", baseURL),
		},
	}

	// 设置标准安全头
	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "public, max-age=3600")
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "DENY")
	c.Header("X-XSS-Protection", "1; mode=block")
	c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
	c.Header("Content-Security-Policy", "default-src 'none'; frame-ancestors 'none'")
	c.JSON(http.StatusOK, config)
}

// HandleSSFConfiguration 处理SSF配置发现端点
// 基于Authentik SSF企业版实现模式
func (h *AppleBusinessManagerHandler) HandleSSFConfiguration(c *gin.Context) {
	// 动态构建issuer基于请求 - 使用HTTPS确保Apple Business Manager兼容性
	baseURL := fmt.Sprintf("https://%s", c.Request.Host)
	issuer := fmt.Sprintf("%s/application/o/abm", baseURL)

	config := map[string]interface{}{
		"issuer":   issuer,
		"jwks_uri": fmt.Sprintf("%s/oauth2/jwks", baseURL),
		"delivery_methods_supported": []string{
			"https://schemas.openid.net/secevent/risc/delivery-method/push",
			"https://schemas.openid.net/secevent/risc/delivery-method/poll",
		},
		"events_supported": []string{
			"https://schemas.openid.net/secevent/risc/event-type/session-revoked",
			"https://schemas.openid.net/secevent/risc/event-type/credential-change",
			"https://schemas.openid.net/secevent/oauth/event-type/token-revoked",
			// Apple Business Manager专用事件
			"https://schemas.apple.com/secevent/abm/event-type/user-logout",
			"https://schemas.apple.com/secevent/abm/event-type/password-change",
			"https://schemas.apple.com/secevent/abm/event-type/authenticator-change",
			"https://schemas.apple.com/secevent/abm/event-type/user-sync-required",
		},
		"critical_subject_members": []string{"sub", "email", "iss"},
		"authorization_schemes": []map[string]interface{}{
			{"spec_urn": "urn:ietf:rfc:6749"},
			{"spec_urn": "urn:ietf:rfc:8693"}, // OAuth 2.0 Token Exchange
		},

		// SET Token配置
		"set_token_signing_alg_values_supported": []string{
			"RS256",
			"ES256",
		},
		"set_token_encryption_alg_values_supported": []string{
			"RSA-OAEP",
			"RSA-OAEP-256",
		},
		"set_token_encryption_enc_values_supported": []string{
			"A128GCM",
			"A192GCM",
			"A256GCM",
		},
		"configuration_endpoint":  fmt.Sprintf("%s/api/ssf/configuration", baseURL),
		"status_endpoint":         fmt.Sprintf("%s/api/ssf/status", baseURL),
		"add_subject_endpoint":    fmt.Sprintf("%s/api/ssf/add-subject", baseURL),
		"remove_subject_endpoint": fmt.Sprintf("%s/api/ssf/remove-subject", baseURL),
		"verification_endpoint":   fmt.Sprintf("%s/api/ssf/verification", baseURL),
		"add_subject": map[string]interface{}{
			"supported":         true,
			"formats_supported": []string{"complex", "email", "opaque"},
		},
		"remove_subject": map[string]interface{}{
			"supported": true,
		},

		// Apple Business Manager专用SSF配置
		"apple_business_manager_ssf": map[string]interface{}{
			"user_sync_events_enabled":       true,
			"password_change_events_enabled": true,
			"session_events_enabled":         true,
			"authenticator_events_enabled":   true,
			"event_delivery_timeout":         30, // 秒
			"max_retry_attempts":             3,
			"retry_backoff_seconds":          []int{1, 5, 15},
			"supported_subject_formats":      []string{"email", "user_id", "complex"},
			"batch_event_support":            true,
			"max_events_per_batch":           50,
		},
	}

	// 设置标准安全头
	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "public, max-age=3600")
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "DENY")
	c.Header("X-XSS-Protection", "1; mode=block")
	c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
	c.Header("Content-Security-Policy", "default-src 'none'; frame-ancestors 'none'")
	c.JSON(http.StatusOK, config)
}

// HandleJWKS 处理JWKS请求
func (h *AppleBusinessManagerHandler) HandleJWKS(c *gin.Context) {
	clientIP := h.getClientIP(c)
	h.logger.Info("JWKS request received", zap.String("ip", clientIP))

	// 生成RSA公钥的JWK格式
	n := base64.RawURLEncoding.EncodeToString(h.publicKey.N.Bytes())
	e := base64.RawURLEncoding.EncodeToString([]byte{1, 0, 1}) // 65537

	// 基于Authentik JWKSView实现模式
	jwks := map[string]interface{}{
		"keys": []map[string]interface{}{
			{
				"kty":     "RSA",
				"use":     "sig",
				"key_ops": []string{"verify"},
				"kid":     "apple-key-1",
				"alg":     "RS256",
				"n":       n,
				"e":       e,
				"x5t":     "",         // X.509证书SHA-1指纹 (可选)
				"x5c":     []string{}, // X.509证书链 (可选)
			},
		},
	}

	// 设置标准安全头
	c.Header("Content-Type", "application/json; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=86400") // 24小时缓存
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "DENY")
	c.Header("X-XSS-Protection", "1; mode=block")
	c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

	h.logger.Info("JWKS response sent successfully")
	c.JSON(http.StatusOK, jwks)
}

// getClientIP 获取客户端真实IP（考虑Cloudflare代理）
func (h *AppleBusinessManagerHandler) getClientIP(c *gin.Context) string {
	// Cloudflare提供的真实IP头
	if ip := c.GetHeader("CF-Connecting-IP"); ip != "" {
		return ip
	}
	// 其他代理头
	if ip := c.GetHeader("X-Forwarded-For"); ip != "" {
		return strings.Split(ip, ",")[0]
	}
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		return ip
	}
	return c.ClientIP()
}

// HandleUserStatus 处理单个用户状态查询
func (h *AppleBusinessManagerHandler) HandleUserStatus(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": "User ID is required",
		})
		return
	}

	// 模拟用户状态查询 (实际实现中应该从数据库查询)
	userStatus := map[string]interface{}{
		"user_id":               userID,
		"status":                "active",
		"last_login_time":       "2025-08-04T15:30:00Z",
		"password_changed_time": "2025-08-04T10:00:00Z",
		"requires_reauth":       false,
		"account_locked":        false,
		"login_attempts":        0,
	}

	h.logger.Info("User status requested",
		zap.String("user_id", userID),
		zap.String("client_ip", c.ClientIP()))

	c.JSON(200, userStatus)
}

// HandleBatchUserStatus 处理批量用户状态查询
func (h *AppleBusinessManagerHandler) HandleBatchUserStatus(c *gin.Context) {
	var request struct {
		UserIDs []string `json:"user_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid batch user status request",
		})
		return
	}

	if len(request.UserIDs) == 0 {
		c.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": "At least one user ID is required",
		})
		return
	}

	if len(request.UserIDs) > 100 {
		c.JSON(400, gin.H{
			"error":             "invalid_request",
			"error_description": "Maximum 100 user IDs allowed per request",
		})
		return
	}

	// 模拟批量用户状态查询
	userStatuses := make([]map[string]interface{}, 0, len(request.UserIDs))
	for _, userID := range request.UserIDs {
		userStatus := map[string]interface{}{
			"user_id":               userID,
			"status":                "active",
			"last_login_time":       "2025-08-04T15:30:00Z",
			"password_changed_time": "2025-08-04T10:00:00Z",
			"requires_reauth":       false,
			"account_locked":        false,
			"login_attempts":        0,
		}
		userStatuses = append(userStatuses, userStatus)
	}

	response := map[string]interface{}{
		"users":       userStatuses,
		"total_count": len(userStatuses),
		"timestamp":   "2025-08-04T15:55:00Z",
	}

	h.logger.Info("Batch user status requested",
		zap.Int("user_count", len(request.UserIDs)),
		zap.String("client_ip", c.ClientIP()))

	c.JSON(200, response)
}

// HandleAppleBusinessManagerConfiguration 处理Apple Business Manager专用配置发现端点
func (h *AppleBusinessManagerHandler) HandleAppleBusinessManagerConfiguration(c *gin.Context) {
	// 动态构建issuer基于请求
	baseURL := fmt.Sprintf("https://%s", c.Request.Host)
	issuer := fmt.Sprintf("%s/application/o/abm", baseURL)

	config := map[string]interface{}{
		"issuer":     issuer,
		"version":    "1.0.0",
		"provider":   "go-abm-idp",
		"created_at": "2025-08-04T16:30:00Z",

		// 基础OIDC端点
		"oidc_configuration_endpoint": fmt.Sprintf("%s/.well-known/openid_configuration", baseURL),
		"authorization_endpoint":      fmt.Sprintf("%s/oauth2/authorize", baseURL),
		"token_endpoint":              fmt.Sprintf("%s/oauth2/token", baseURL),
		"userinfo_endpoint":           fmt.Sprintf("%s/oauth2/userinfo", baseURL),
		"jwks_uri":                    fmt.Sprintf("%s/oauth2/jwks", baseURL),

		// SSF端点
		"ssf_configuration_endpoint": fmt.Sprintf("%s/.well-known/ssf_configuration", baseURL),

		// Apple Business Manager专用功能
		"user_management": map[string]interface{}{
			"user_sync_endpoint":         fmt.Sprintf("%s/application/o/abm/users/sync", baseURL),
			"password_change_endpoint":   fmt.Sprintf("%s/application/o/abm/users/password-changed", baseURL),
			"user_status_endpoint":       fmt.Sprintf("%s/application/o/abm/users/{user_id}/status", baseURL),
			"batch_user_status_endpoint": fmt.Sprintf("%s/application/o/abm/users/status", baseURL),

			"supported_sync_types": []string{"full", "incremental", "delta"},
			"supported_permissions": []string{
				"read_users",
				"read_user_profiles",
				"read_user_status",
				"read_user_roles",
				"read_login_history",
				"read_password_status",
			},

			"sync_limits": map[string]interface{}{
				"max_users_per_sync":         1000,
				"max_users_per_batch_status": 100,
				"sync_timeout_seconds":       300,
				"rate_limit_per_minute":      10,
			},

			"user_attributes": []string{
				"user_id",
				"username",
				"email",
				"display_name",
				"given_name",
				"family_name",
				"status",
				"roles",
				"department",
				"last_login_time",
				"password_changed_time",
				"requires_reauth",
				"account_locked",
				"login_attempts",
			},
		},

		// SSF事件配置
		"ssf_events": map[string]interface{}{
			"supported_events": []string{
				"user_logout",
				"password_change",
				"authenticator_change",
				"session_revoke",
				"user_sync_required",
			},

			"delivery_methods":       []string{"push", "poll"},
			"event_delivery_timeout": 30,
			"max_retry_attempts":     3,
			"retry_backoff_seconds":  []int{1, 5, 15},
			"batch_event_support":    true,
			"max_events_per_batch":   50,
		},

		// 安全配置
		"security": map[string]interface{}{
			"required_tls_version":    "1.2",
			"supported_auth_methods":  []string{"client_secret_post", "client_secret_basic"},
			"pkce_required":           true,
			"pkce_methods_supported":  []string{"S256", "plain"},
			"token_signing_algorithm": "RS256",
			"token_expiry_seconds":    3600,
		},

		// 兼容性信息
		"compatibility": map[string]interface{}{
			"apple_business_manager_version": "latest",
			"openid_connect_version":         "1.0",
			"oauth2_version":                 "2.0",
			"ssf_version":                    "1.0",
			"supported_locales":              []string{"en-US", "zh-CN"},
		},

		// 支持和文档
		"support": map[string]interface{}{
			"documentation_url": fmt.Sprintf("%s/docs/apple-business-manager", baseURL),
			"support_email":     "<EMAIL>",
			"status_page":       fmt.Sprintf("%s/status", baseURL),
		},
	}

	// 设置标准安全头
	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "public, max-age=3600")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "DENY")
	c.Header("X-XSS-Protection", "1; mode=block")
	c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
	c.Header("Content-Security-Policy", "default-src 'none'; frame-ancestors 'none'")

	h.logger.Info("Apple Business Manager configuration requested",
		zap.String("client_ip", c.ClientIP()))

	c.JSON(200, config)
}
