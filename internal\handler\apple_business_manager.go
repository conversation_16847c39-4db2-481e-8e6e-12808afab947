package handler

import (
	"crypto/rand"
	"crypto/rsa"
	"encoding/base64"
	"fmt"
	"go-abm-idp/pkg/log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

// AppleBusinessManagerHandler Apple Business Manager专用处理器
type AppleBusinessManagerHandler struct {
	logger       *log.Logger
	privateKey   *rsa.PrivateKey
	publicKey    *rsa.PublicKey
	jwtSecret    string
	issuer       string
	clientID     string
	clientSecret string
}

// NewAppleBusinessManagerHandler 创建Apple Business Manager处理器
func NewAppleBusinessManagerHandler(logger *log.Logger) (*AppleBusinessManagerHandler, error) {
	// 生成RSA密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %w", err)
	}

	return &AppleBusinessManagerHandler{
		logger:       logger,
		privateKey:   privateKey,
		publicKey:    &privateKey.PublicKey,
		jwtSecret:    "apple-business-manager-jwt-secret-2024-secure", // 在生产环境中从配置读取
		issuer:       "http://localhost:8000",                         // 使用实际可访问的端点
		clientID:     "go-abm-idp-client",
		clientSecret: "ABM-GoIDP-2024-SecureSecret-Production",
	}, nil
}

// AppleTokenRequest Apple令牌请求
type AppleTokenRequest struct {
	GrantType    string `form:"grant_type" json:"grant_type"`
	Code         string `form:"code" json:"code"`
	RedirectURI  string `form:"redirect_uri" json:"redirect_uri"`
	ClientID     string `form:"client_id" json:"client_id"`
	ClientSecret string `form:"client_secret" json:"client_secret"`
	CodeVerifier string `form:"code_verifier" json:"code_verifier"`
}

// AppleTokenResponse Apple令牌响应
type AppleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token,omitempty"`
	IDToken      string `json:"id_token,omitempty"`
	Scope        string `json:"scope,omitempty"`
}

// AppleUserInfoResponse Apple用户信息响应
type AppleUserInfoResponse struct {
	Sub               string `json:"sub"`
	Name              string `json:"name,omitempty"`
	GivenName         string `json:"given_name,omitempty"`
	FamilyName        string `json:"family_name,omitempty"`
	Email             string `json:"email,omitempty"`
	EmailVerified     bool   `json:"email_verified,omitempty"`
	PreferredUsername string `json:"preferred_username,omitempty"`
}

// HandleAppleToken 处理Apple Business Manager令牌请求
func (h *AppleBusinessManagerHandler) HandleAppleToken(c *gin.Context) {
	// 记录详细的请求信息
	clientIP := h.getClientIP(c)
	userAgent := c.GetHeader("User-Agent")
	cfRay := c.GetHeader("CF-Ray")

	h.logger.Info("Apple Business Manager token request received",
		zap.String("ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.String("cf_ray", cfRay),
		zap.String("method", c.Request.Method),
		zap.String("content_type", c.GetHeader("Content-Type")),
	)

	var req AppleTokenRequest

	// 解析请求
	if err := c.ShouldBind(&req); err != nil {
		h.logger.Error("Token request parsing failed", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid request parameters",
		})
		return
	}

	// 记录请求参数（敏感信息除外）
	h.logger.Debug("Token request parameters",
		zap.String("grant_type", req.GrantType),
		zap.String("client_id", req.ClientID),
		zap.Bool("has_code", req.Code != ""),
		zap.Bool("has_redirect_uri", req.RedirectURI != ""),
		zap.Bool("has_code_verifier", req.CodeVerifier != ""),
	)

	// 验证grant_type
	if req.GrantType != "authorization_code" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "unsupported_grant_type",
			"error_description": "Only authorization_code grant type is supported",
		})
		return
	}

	// 记录客户端ID但不严格验证（Apple可能使用不同的client_id）
	h.logger.Info("Token request from client",
		zap.String("client_id", req.ClientID),
		zap.String("expected_client_id", h.clientID),
	)

	// 验证授权码（在实际实现中，这里应该验证真实的授权码）
	if req.Code == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_grant",
			"error_description": "Invalid authorization code",
		})
		return
	}

	// 生成访问令牌
	accessToken, err := h.generateAccessToken("apple-user-123")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate access token",
		})
		return
	}

	// 生成ID令牌（Apple Business Manager需要）
	idToken, err := h.generateIDToken("apple-user-123", req.ClientID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate ID token",
		})
		return
	}

	// 生成刷新令牌
	refreshToken := h.generateRefreshToken()

	// 返回令牌响应 - 包含Apple请求的所有scope，永不过期
	response := AppleTokenResponse{
		AccessToken:  accessToken,
		TokenType:    "Bearer",
		ExpiresIn:    3153600000, // 100年的秒数，实际上永不过期
		RefreshToken: refreshToken,
		IDToken:      idToken,
		Scope:        "openid profile email ssf.manage ssf.read offline_access",
	}

	// 记录响应详情
	h.logger.Info("Token response generated successfully",
		zap.Int("access_token_length", len(accessToken)),
		zap.Int("id_token_length", len(idToken)),
		zap.Int("refresh_token_length", len(refreshToken)),
		zap.String("scope", response.Scope),
	)

	// 设置Apple Business Manager要求的响应头
	c.Header("Cache-Control", "no-store")
	c.Header("Pragma", "no-cache")
	c.JSON(http.StatusOK, response)
}

// HandleAppleUserInfo 处理Apple Business Manager用户信息请求
func (h *AppleBusinessManagerHandler) HandleAppleUserInfo(c *gin.Context) {
	// 获取Authorization头
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Missing access token",
		})
		return
	}

	// 验证Bearer令牌格式
	if !strings.HasPrefix(authHeader, "Bearer ") {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Invalid token format",
		})
		return
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")

	// 验证访问令牌
	userID, err := h.validateAccessToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":             "invalid_token",
			"error_description": "Invalid or expired access token",
		})
		return
	}

	// 返回用户信息（根据实际用户数据调整）
	userInfo := AppleUserInfoResponse{
		Sub:               userID,
		Name:              "Apple Business Manager User",
		GivenName:         "Apple",
		FamilyName:        "User",
		Email:             "<EMAIL>",
		EmailVerified:     true,
		PreferredUsername: "appleuser",
	}

	c.JSON(http.StatusOK, userInfo)
}

// generateAccessToken 生成访问令牌 (RS256) - 永不过期
func (h *AppleBusinessManagerHandler) generateAccessToken(userID string) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"sub":   userID,
		"iss":   h.issuer,
		"aud":   []string{h.clientID, h.issuer},
		"exp":   now.Add(100 * 365 * 24 * time.Hour).Unix(), // 100年后过期，实际上永不过期
		"iat":   now.Unix(),
		"nbf":   now.Unix(),
		"jti":   h.generateJTI(),
		"scope": "openid profile email ssf.manage ssf.read offline_access",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = "apple-key-1"
	return token.SignedString(h.privateKey)
}

// generateIDToken 生成ID令牌 (RS256) - 永不过期
func (h *AppleBusinessManagerHandler) generateIDToken(userID, audience string) (string, error) {
	if audience == "" {
		audience = h.clientID
	}

	now := time.Now()
	claims := jwt.MapClaims{
		"sub":       userID,
		"iss":       h.issuer,
		"aud":       audience,
		"exp":       now.Add(100 * 365 * 24 * time.Hour).Unix(), // 100年后过期，实际上永不过期
		"iat":       now.Unix(),
		"nbf":       now.Unix(),
		"auth_time": now.Unix(),
		"jti":       h.generateJTI(),
		// Apple Business Manager需要的用户信息
		"name":               "Apple Business Manager User",
		"given_name":         "Apple",
		"family_name":        "User",
		"email":              "<EMAIL>",
		"email_verified":     true,
		"preferred_username": "appleuser",
		"updated_at":         now.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = "apple-key-1"
	return token.SignedString(h.privateKey)
}

// generateRefreshToken 生成刷新令牌
func (h *AppleBusinessManagerHandler) generateRefreshToken() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// generateJTI 生成JWT ID
func (h *AppleBusinessManagerHandler) generateJTI() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// generateNonce 生成nonce
func (h *AppleBusinessManagerHandler) generateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// validateAccessToken 验证访问令牌 (RS256)
func (h *AppleBusinessManagerHandler) validateAccessToken(tokenString string) (string, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return h.publicKey, nil
	})

	if err != nil {
		return "", err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		if sub, ok := claims["sub"].(string); ok {
			return sub, nil
		}
	}

	return "", fmt.Errorf("invalid token claims")
}

// HandleAppleWellKnown 处理Apple Business Manager的OIDC配置发现
func (h *AppleBusinessManagerHandler) HandleAppleWellKnown(c *gin.Context) {
	config := map[string]interface{}{
		"issuer":                 h.issuer,
		"authorization_endpoint": fmt.Sprintf("%s/oauth2/authorize", h.issuer),
		"token_endpoint":         fmt.Sprintf("%s/application/o/token/", h.issuer),
		"userinfo_endpoint":      fmt.Sprintf("%s/application/o/userinfo/", h.issuer),
		"jwks_uri":               fmt.Sprintf("%s/application/o/default/jwks/", h.issuer),
		"scopes_supported": []string{
			"openid",
			"profile",
			"email",
			"offline_access",
			"ssf.manage",
			"ssf.read",
		},
		"response_types_supported": []string{
			"code",
		},
		"grant_types_supported": []string{
			"authorization_code",
			"refresh_token",
		},
		"subject_types_supported": []string{
			"public",
		},
		"id_token_signing_alg_values_supported": []string{
			"RS256",
		},
		"token_endpoint_auth_methods_supported": []string{
			"client_secret_post",
			"client_secret_basic",
		},
		"claims_supported": []string{
			"sub",
			"iss",
			"aud",
			"exp",
			"iat",
			"name",
			"given_name",
			"family_name",
			"email",
			"email_verified",
			"preferred_username",
		},
		"code_challenge_methods_supported": []string{
			"S256",
		},
	}

	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "public, max-age=3600")
	c.JSON(http.StatusOK, config)
}

// HandleSSFConfiguration 处理SSF配置发现端点
func (h *AppleBusinessManagerHandler) HandleSSFConfiguration(c *gin.Context) {
	config := map[string]interface{}{
		"issuer":   h.issuer,
		"jwks_uri": fmt.Sprintf("%s/oauth2/jwks", h.issuer),
		"delivery_methods_supported": []string{
			"https://schemas.openid.net/secevent/risc/delivery-method/push",
		},
		"events_supported": []string{
			"https://schemas.openid.net/secevent/risc/event-type/session-revoked",
			"https://schemas.openid.net/secevent/risc/event-type/credential-change",
			"https://schemas.openid.net/secevent/oauth/event-type/token-revoked",
		},
		"critical_subject_members": []string{"sub", "email"},
		"authorization_schemes": []map[string]interface{}{
			{"spec_urn": "urn:ietf:rfc:6749"},
		},
		"configuration_endpoint": fmt.Sprintf("%s/ssf/stream", h.issuer),
		"add_subject": map[string]interface{}{
			"supported":         true,
			"formats_supported": []string{"complex", "email", "opaque"},
		},
		"remove_subject": map[string]interface{}{
			"supported": true,
		},
	}

	c.Header("Content-Type", "application/json")
	c.Header("Cache-Control", "public, max-age=3600")
	c.JSON(http.StatusOK, config)
}

// HandleJWKS 处理JWKS请求
func (h *AppleBusinessManagerHandler) HandleJWKS(c *gin.Context) {
	clientIP := h.getClientIP(c)
	h.logger.Info("JWKS request received", zap.String("ip", clientIP))

	// 生成RSA公钥的JWK格式
	n := base64.RawURLEncoding.EncodeToString(h.publicKey.N.Bytes())
	e := base64.RawURLEncoding.EncodeToString([]byte{1, 0, 1}) // 65537

	jwks := map[string]interface{}{
		"keys": []map[string]interface{}{
			{
				"kty": "RSA",
				"use": "sig",
				"kid": "apple-key-1",
				"alg": "RS256",
				"n":   n,
				"e":   e,
			},
		},
	}

	c.Header("Content-Type", "application/json; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=86400") // 24小时缓存
	c.Header("Access-Control-Allow-Origin", "*")

	h.logger.Info("JWKS response sent successfully")
	c.JSON(http.StatusOK, jwks)
}

// getClientIP 获取客户端真实IP（考虑Cloudflare代理）
func (h *AppleBusinessManagerHandler) getClientIP(c *gin.Context) string {
	// Cloudflare提供的真实IP头
	if ip := c.GetHeader("CF-Connecting-IP"); ip != "" {
		return ip
	}
	// 其他代理头
	if ip := c.GetHeader("X-Forwarded-For"); ip != "" {
		return strings.Split(ip, ",")[0]
	}
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		return ip
	}
	return c.ClientIP()
}
