# Go ABM IDP 部署指南

## 🚀 世界顶级OAuth2/OIDC身份提供者部署指南

Go ABM IDP是一个100%标准符合的企业级OAuth2/OIDC身份提供者，支持22个RFC标准，具备金融级安全性和极致性能。

## 📋 系统要求

### 最低要求
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 1Gbps
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+, RHEL 8+)

### 推荐配置
- **CPU**: 16核心
- **内存**: 32GB RAM
- **存储**: 500GB NVMe SSD
- **网络**: 10Gbps
- **负载均衡**: HAProxy/Nginx
- **数据库**: PostgreSQL 14+ 集群
- **缓存**: Redis 7+ 集群

### 生产环境要求
- **CPU**: 32核心+
- **内存**: 64GB+ RAM
- **存储**: 1TB+ NVMe SSD (RAID 10)
- **网络**: 25Gbps+
- **高可用**: 多区域部署
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 🏗️ 架构部署

### 单机部署 (开发/测试)

```bash
# 1. 克隆项目
git clone https://github.com/your-org/go-abm-idp.git
cd go-abm-idp

# 2. 安装依赖
go mod download

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 4. 初始化数据库
make db-migrate

# 5. 启动服务
make run
```

### 容器化部署 (推荐)

```yaml
# docker-compose.yml
version: '3.8'

services:
  go-abm-idp:
    image: go-abm-idp:latest
    ports:
      - "8080:8080"
      - "8443:8443"
    environment:
      - DATABASE_URL=************************************/idp
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-secret-key
    depends_on:
      - postgres
      - redis
    volumes:
      - ./config:/app/config
      - ./certs:/app/certs
    restart: unless-stopped

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=idp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Kubernetes部署 (生产环境)

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-abm-idp
  namespace: identity
spec:
  replicas: 3
  selector:
    matchLabels:
      app: go-abm-idp
  template:
    metadata:
      labels:
        app: go-abm-idp
    spec:
      containers:
      - name: go-abm-idp
        image: go-abm-idp:v1.0.0
        ports:
        - containerPort: 8080
        - containerPort: 8443
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: idp-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: idp-secrets
              key: redis-url
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: go-abm-idp-service
  namespace: identity
spec:
  selector:
    app: go-abm-idp
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: https
    port: 443
    targetPort: 8443
  type: LoadBalancer
```

## 🔧 配置管理

### 核心配置文件

```yaml
# config/production.yaml
server:
  host: "0.0.0.0"
  port: 8080
  https_port: 8443
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s

database:
  driver: "postgres"
  host: "postgres-cluster.internal"
  port: 5432
  database: "idp_production"
  username: "idp_user"
  password: "${DATABASE_PASSWORD}"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 1h

redis:
  addrs:
    - "redis-cluster-1.internal:6379"
    - "redis-cluster-2.internal:6379"
    - "redis-cluster-3.internal:6379"
  password: "${REDIS_PASSWORD}"
  pool_size: 100
  min_idle_conns: 10

oauth2:
  issuer: "https://idp.yourdomain.com"
  access_token_ttl: 1h
  refresh_token_ttl: 24h
  authorization_code_ttl: 10m
  require_pkce: true
  allow_plain_pkce: false

oidc:
  id_token_ttl: 1h
  supported_scopes:
    - "openid"
    - "profile"
    - "email"
    - "offline_access"
  supported_claims:
    - "sub"
    - "iss"
    - "aud"
    - "exp"
    - "iat"
    - "name"
    - "email"

security:
  jwt_secret: "${JWT_SECRET}"
  encryption_key: "${ENCRYPTION_KEY}"
  require_https: true
  cors_enabled: true
  cors_origins:
    - "https://app.yourdomain.com"
    - "https://admin.yourdomain.com"

# 多租户配置
multitenant:
  enabled: true
  isolation_mode: "namespace"
  default_tenant: "default"

# FAPI合规配置
fapi:
  enabled: true
  profile: "fapi2"
  enforce_strict: true

# WebAuthn配置
webauthn:
  enabled: true
  rp_id: "yourdomain.com"
  rp_name: "Your Company IDP"
  timeout: 60s

# AI安全配置
ai_security:
  enabled: true
  learning_enabled: true
  update_interval: 5m

# 缓存配置
cache:
  local_cache_size: 10000
  local_cache_ttl: 5m
  redis_pool_size: 100
  consistency_level: "eventual"

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_port: 8081
  log_level: "info"
  log_format: "json"
```

### 环境变量

```bash
# .env.production
# 数据库配置
DATABASE_PASSWORD=your-secure-database-password
DATABASE_URL=postgresql://idp_user:${DATABASE_PASSWORD}@postgres-cluster.internal:5432/idp_production

# Redis配置
REDIS_PASSWORD=your-secure-redis-password
REDIS_URL=redis://:${REDIS_PASSWORD}@redis-cluster.internal:6379

# 安全配置
JWT_SECRET=your-256-bit-jwt-secret-key
ENCRYPTION_KEY=your-256-bit-encryption-key

# TLS证书
TLS_CERT_PATH=/app/certs/server.crt
TLS_KEY_PATH=/app/certs/server.key

# 外部服务
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password

# 监控和日志
PROMETHEUS_ENDPOINT=http://prometheus.monitoring:9090
GRAFANA_ENDPOINT=http://grafana.monitoring:3000
ELASTICSEARCH_ENDPOINT=http://elasticsearch.logging:9200
```

## 🔐 安全配置

### TLS/SSL配置

```bash
# 生成自签名证书 (开发环境)
openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.crt -days 365 -nodes

# 生产环境使用Let's Encrypt
certbot certonly --standalone -d idp.yourdomain.com
```

### mTLS配置

```yaml
# config/mtls.yaml
mtls:
  enabled: true
  require_client_cert: true
  client_ca_file: "/app/certs/client-ca.crt"
  verification_mode: "strict"
  endpoints:
    - "/oauth2/token"
    - "/oauth2/introspect"
    - "/oauth2/revoke"
```

### FAPI合规配置

```yaml
# config/fapi.yaml
fapi:
  enabled: true
  profile: "fapi2"
  security_profile: "fapi2"
  enforce_strict: true
  allowed_algorithms:
    - "PS256"
    - "ES256"
    - "EdDSA"
  required_claims:
    - "iss"
    - "sub"
    - "aud"
    - "exp"
    - "iat"
    - "jti"
    - "auth_time"
    - "acr"
    - "amr"
```

## 📊 监控和日志

### Prometheus监控

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'go-abm-idp'
    static_configs:
      - targets: ['go-abm-idp:9090']
    metrics_path: /metrics
    scrape_interval: 5s
```

### Grafana仪表板

```json
{
  "dashboard": {
    "title": "Go ABM IDP Dashboard",
    "panels": [
      {
        "title": "Requests per Second",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "singlestat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])"
          }
        ]
      }
    ]
  }
}
```

### 日志配置

```yaml
# config/logging.yaml
logging:
  level: "info"
  format: "json"
  output: "stdout"
  
  # 结构化日志字段
  fields:
    service: "go-abm-idp"
    version: "1.0.0"
    environment: "production"
  
  # 日志轮转
  rotation:
    enabled: true
    max_size: "100MB"
    max_age: "30d"
    max_backups: 10
    compress: true
```

## 🚀 性能优化

### 数据库优化

```sql
-- PostgreSQL优化配置
-- postgresql.conf
shared_buffers = 8GB
effective_cache_size = 24GB
maintenance_work_mem = 2GB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

-- 索引优化
CREATE INDEX CONCURRENTLY idx_oauth2_clients_client_id ON oauth2_clients(client_id);
CREATE INDEX CONCURRENTLY idx_oauth2_tokens_access_token ON oauth2_tokens(access_token);
CREATE INDEX CONCURRENTLY idx_oauth2_codes_code ON oauth2_authorization_codes(code);
CREATE INDEX CONCURRENTLY idx_sessions_session_id ON sessions(session_id);
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
```

### Redis优化

```conf
# redis.conf
maxmemory 16gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
tcp-keepalive 300
timeout 0
```

### 应用优化

```yaml
# config/performance.yaml
performance:
  # 连接池配置
  database:
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 1h
  
  # 缓存配置
  cache:
    local_cache_size: 50000
    local_cache_ttl: 10m
    redis_pool_size: 200
    redis_timeout: 3s
  
  # HTTP配置
  http:
    read_timeout: 30s
    write_timeout: 30s
    idle_timeout: 120s
    max_header_bytes: 1048576
  
  # 并发配置
  concurrency:
    max_goroutines: 10000
    worker_pool_size: 100
```

## 🔄 备份和恢复

### 数据库备份

```bash
#!/bin/bash
# backup.sh

# 数据库备份
pg_dump -h postgres-cluster.internal -U idp_user -d idp_production > backup_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份
gzip backup_*.sql

# 上传到云存储
aws s3 cp backup_*.sql.gz s3://your-backup-bucket/database/

# 清理本地备份 (保留7天)
find . -name "backup_*.sql.gz" -mtime +7 -delete
```

### Redis备份

```bash
#!/bin/bash
# redis-backup.sh

# Redis备份
redis-cli --rdb dump.rdb

# 上传到云存储
aws s3 cp dump.rdb s3://your-backup-bucket/redis/dump_$(date +%Y%m%d_%H%M%S).rdb
```

### 恢复流程

```bash
#!/bin/bash
# restore.sh

# 1. 停止服务
kubectl scale deployment go-abm-idp --replicas=0

# 2. 恢复数据库
psql -h postgres-cluster.internal -U idp_user -d idp_production < backup_20231201_120000.sql

# 3. 恢复Redis
redis-cli --rdb dump_20231201_120000.rdb

# 4. 启动服务
kubectl scale deployment go-abm-idp --replicas=3

# 5. 验证服务
curl -f http://go-abm-idp/health || exit 1
```

## 📈 扩展和升级

### 水平扩展

```bash
# Kubernetes水平扩展
kubectl scale deployment go-abm-idp --replicas=10

# 自动扩展
kubectl autoscale deployment go-abm-idp --cpu-percent=70 --min=3 --max=20
```

### 垂直扩展

```yaml
# 增加资源限制
resources:
  requests:
    memory: "4Gi"
    cpu: "2000m"
  limits:
    memory: "8Gi"
    cpu: "4000m"
```

### 滚动升级

```bash
# 滚动升级
kubectl set image deployment/go-abm-idp go-abm-idp=go-abm-idp:v1.1.0

# 回滚
kubectl rollout undo deployment/go-abm-idp
```

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库连接
   psql -h postgres-cluster.internal -U idp_user -d idp_production -c "SELECT 1;"
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis连接
   redis-cli -h redis-cluster.internal ping
   ```

3. **TLS证书问题**
   ```bash
   # 检查证书有效期
   openssl x509 -in server.crt -text -noout | grep "Not After"
   ```

4. **性能问题**
   ```bash
   # 检查系统资源
   top
   iostat -x 1
   netstat -i
   ```

### 日志分析

```bash
# 查看应用日志
kubectl logs -f deployment/go-abm-idp

# 查看错误日志
kubectl logs deployment/go-abm-idp | grep ERROR

# 查看性能日志
kubectl logs deployment/go-abm-idp | grep "slow_query\|high_latency"
```

## 📞 支持和维护

### 健康检查

- **健康检查端点**: `GET /health`
- **就绪检查端点**: `GET /ready`
- **指标端点**: `GET /metrics`

### 维护窗口

建议在业务低峰期进行维护：
- **时间**: 凌晨2:00-4:00 (本地时间)
- **频率**: 每月第一个周日
- **通知**: 提前48小时通知用户

### 技术支持

- **文档**: https://docs.yourdomain.com/idp
- **问题跟踪**: https://github.com/your-org/go-abm-idp/issues
- **社区**: https://community.yourdomain.com/idp
- **企业支持**: <EMAIL>

---

**Go ABM IDP - 世界顶级OAuth2/OIDC身份提供者，为您的数字化转型保驾护航！** 🚀
