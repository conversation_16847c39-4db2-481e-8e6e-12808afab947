# Generated by Django 4.2.4 on 2023-08-31 10:42

from django.db import migrations, models

import authentik.policies.reputation.models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies_reputation", "0004_reputationpolicy_authentik_p_policy__8f0d70_idx"),
    ]

    operations = [
        migrations.AddField(
            model_name="reputation",
            name="expires",
            field=models.DateTimeField(
                default=authentik.policies.reputation.models.reputation_expiry
            ),
        ),
        migrations.AddField(
            model_name="reputation",
            name="expiring",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterModelOptions(
            name="reputation",
            options={
                "verbose_name": "Reputation Score",
                "verbose_name_plural": "Reputation Scores",
            },
        ),
    ]
