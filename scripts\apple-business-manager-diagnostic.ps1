# Apple Business Manager错误码-27480诊断工具
# 专门用于诊断和验证Apple Business Manager集成问题

param(
    [string]$BaseURL = "https://openid.akapril.in",
    [switch]$Verbose,
    [switch]$FixMode
)

Write-Host "🍎 Apple Business Manager错误码-27480诊断工具" -ForegroundColor Green
Write-Host "目标URL: $BaseURL" -ForegroundColor Cyan
Write-Host "诊断时间: $(Get-Date)" -ForegroundColor Gray

# 诊断结果
$DiagnosticResults = @{
    Passed = 0
    Failed = 0
    Warnings = 0
    Total = 0
}

# 诊断函数
function Test-AppleRequirement {
    param(
        [string]$TestName,
        [scriptblock]$TestScript,
        [string]$Requirement,
        [string]$FixSuggestion = ""
    )
    
    $DiagnosticResults.Total++
    Write-Host "`n🔍 测试: $TestName" -ForegroundColor Yellow
    Write-Host "   要求: $Requirement" -ForegroundColor Gray
    
    try {
        $result = & $TestScript
        if ($result.Success) {
            Write-Host "   ✅ 通过: $($result.Message)" -ForegroundColor Green
            $DiagnosticResults.Passed++
        } elseif ($result.Warning) {
            Write-Host "   ⚠️  警告: $($result.Message)" -ForegroundColor Yellow
            if ($FixSuggestion) {
                Write-Host "   💡 建议: $FixSuggestion" -ForegroundColor Cyan
            }
            $DiagnosticResults.Warnings++
        } else {
            Write-Host "   ❌ 失败: $($result.Message)" -ForegroundColor Red
            if ($FixSuggestion) {
                Write-Host "   🔧 修复: $FixSuggestion" -ForegroundColor Cyan
            }
            $DiagnosticResults.Failed++
        }
        
        if ($Verbose -and $result.Details) {
            Write-Host "   📋 详情: $($result.Details)" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "   ❌ 异常: $($_.Exception.Message)" -ForegroundColor Red
        $DiagnosticResults.Failed++
    }
}

# 测试1: HTTPS要求
Test-AppleRequirement -TestName "HTTPS协议要求" -Requirement "所有端点必须使用HTTPS" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        $httpsEndpoints = @(
            $oidcConfig.issuer,
            $oidcConfig.authorization_endpoint,
            $oidcConfig.token_endpoint,
            $oidcConfig.userinfo_endpoint,
            $oidcConfig.jwks_uri
        )
        
        $nonHttpsEndpoints = $httpsEndpoints | Where-Object { $_ -and -not $_.StartsWith("https://") }
        
        if ($nonHttpsEndpoints.Count -eq 0) {
            return @{ Success = $true; Message = "所有端点都使用HTTPS" }
        } else {
            return @{ Success = $false; Message = "发现非HTTPS端点: $($nonHttpsEndpoints -join ', ')" }
        }
    } catch {
        return @{ Success = $false; Message = "无法获取OIDC配置: $($_.Exception.Message)" }
    }
} -FixSuggestion "确保所有端点URL使用https://前缀"

# 测试2: Issuer格式验证
Test-AppleRequirement -TestName "Issuer格式验证" -Requirement "Issuer必须是有效的HTTPS URL" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        if (-not $oidcConfig.issuer) {
            return @{ Success = $false; Message = "Issuer字段缺失" }
        }
        
        if (-not $oidcConfig.issuer.StartsWith("https://")) {
            return @{ Success = $false; Message = "Issuer不是HTTPS URL: $($oidcConfig.issuer)" }
        }
        
        # 验证Issuer URL格式
        try {
            $uri = [System.Uri]::new($oidcConfig.issuer)
            if ($uri.Scheme -ne "https") {
                return @{ Success = $false; Message = "Issuer协议不是HTTPS" }
            }
        } catch {
            return @{ Success = $false; Message = "Issuer URL格式无效" }
        }
        
        return @{ Success = $true; Message = "Issuer格式正确: $($oidcConfig.issuer)" }
    } catch {
        return @{ Success = $false; Message = "无法验证Issuer: $($_.Exception.Message)" }
    }
} -FixSuggestion "确保issuer字段是完整的HTTPS URL"

# 测试3: JWKS端点可访问性
Test-AppleRequirement -TestName "JWKS端点可访问性" -Requirement "JWKS端点必须可访问且返回有效密钥" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        $jwksResponse = Invoke-RestMethod -Uri $oidcConfig.jwks_uri -UseBasicParsing
        
        if (-not $jwksResponse.keys) {
            return @{ Success = $false; Message = "JWKS响应缺少keys字段" }
        }
        
        if ($jwksResponse.keys.Count -eq 0) {
            return @{ Success = $false; Message = "JWKS中没有密钥" }
        }
        
        # 验证密钥格式
        $firstKey = $jwksResponse.keys[0]
        $requiredFields = @("kty", "use", "kid", "alg", "n", "e")
        $missingFields = $requiredFields | Where-Object { -not $firstKey.$_ }
        
        if ($missingFields.Count -gt 0) {
            return @{ Success = $false; Message = "密钥缺少必需字段: $($missingFields -join ', ')" }
        }
        
        return @{ Success = $true; Message = "JWKS端点正常，包含 $($jwksResponse.keys.Count) 个密钥" }
    } catch {
        return @{ Success = $false; Message = "JWKS端点访问失败: $($_.Exception.Message)" }
    }
} -FixSuggestion "检查JWKS端点URL和密钥格式"

# 测试4: 必需的OIDC字段
Test-AppleRequirement -TestName "OIDC配置完整性" -Requirement "必须包含Apple Business Manager要求的所有字段" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        $requiredFields = @(
            "issuer",
            "authorization_endpoint", 
            "token_endpoint",
            "userinfo_endpoint",
            "jwks_uri",
            "scopes_supported",
            "response_types_supported",
            "grant_types_supported",
            "subject_types_supported",
            "id_token_signing_alg_values_supported",
            "token_endpoint_auth_methods_supported"
        )
        
        $missingFields = $requiredFields | Where-Object { -not $oidcConfig.$_ }
        
        if ($missingFields.Count -gt 0) {
            return @{ Success = $false; Message = "缺少必需字段: $($missingFields -join ', ')" }
        }
        
        return @{ Success = $true; Message = "包含所有必需的OIDC字段" }
    } catch {
        return @{ Success = $false; Message = "无法验证OIDC配置: $($_.Exception.Message)" }
    }
} -FixSuggestion "添加缺少的OIDC配置字段"

# 测试5: PKCE支持验证
Test-AppleRequirement -TestName "PKCE支持验证" -Requirement "必须支持PKCE S256方法" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        if (-not $oidcConfig.code_challenge_methods_supported) {
            return @{ Success = $false; Message = "缺少code_challenge_methods_supported字段" }
        }
        
        if ($oidcConfig.code_challenge_methods_supported -notcontains "S256") {
            return @{ Success = $false; Message = "不支持S256 PKCE方法" }
        }
        
        return @{ Success = $true; Message = "支持PKCE S256方法" }
    } catch {
        return @{ Success = $false; Message = "无法验证PKCE支持: $($_.Exception.Message)" }
    }
} -FixSuggestion "添加code_challenge_methods_supported字段并包含S256"

# 测试6: 响应头验证
Test-AppleRequirement -TestName "HTTP响应头验证" -Requirement "必须返回正确的Content-Type和安全头" -TestScript {
    try {
        $response = Invoke-WebRequest -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        $contentType = $response.Headers["Content-Type"]
        if (-not $contentType -or -not $contentType.StartsWith("application/json")) {
            return @{ Success = $false; Message = "Content-Type不正确: $contentType" }
        }
        
        # 检查安全头
        $securityHeaders = @("X-Content-Type-Options", "X-Frame-Options")
        $missingHeaders = $securityHeaders | Where-Object { -not $response.Headers[$_] }
        
        if ($missingHeaders.Count -gt 0) {
            return @{ Warning = $true; Message = "缺少安全头: $($missingHeaders -join ', ')" }
        }
        
        return @{ Success = $true; Message = "响应头正确" }
    } catch {
        return @{ Success = $false; Message = "无法验证响应头: $($_.Exception.Message)" }
    }
} -FixSuggestion "添加正确的Content-Type和安全响应头"

# 测试7: SSL/TLS证书验证
Test-AppleRequirement -TestName "SSL/TLS证书验证" -Requirement "HTTPS证书必须有效" -TestScript {
    try {
        # 尝试访问端点，如果SSL证书无效会抛出异常
        $null = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        return @{ Success = $true; Message = "SSL/TLS证书有效" }
    } catch {
        if ($_.Exception.Message -like "*SSL*" -or $_.Exception.Message -like "*certificate*") {
            return @{ Success = $false; Message = "SSL/TLS证书问题: $($_.Exception.Message)" }
        } else {
            return @{ Success = $true; Message = "SSL/TLS证书验证通过" }
        }
    }
} -FixSuggestion "确保使用有效的SSL/TLS证书"

# 输出诊断结果
Write-Host "`n📊 Apple Business Manager兼容性诊断结果" -ForegroundColor Magenta
Write-Host "总测试数: $($DiagnosticResults.Total)" -ForegroundColor Cyan
Write-Host "通过: $($DiagnosticResults.Passed)" -ForegroundColor Green
Write-Host "警告: $($DiagnosticResults.Warnings)" -ForegroundColor Yellow
Write-Host "失败: $($DiagnosticResults.Failed)" -ForegroundColor Red

$compatibilityScore = if ($DiagnosticResults.Total -gt 0) { 
    [math]::Round((($DiagnosticResults.Passed + $DiagnosticResults.Warnings * 0.5) / $DiagnosticResults.Total) * 100, 2) 
} else { 0 }

Write-Host "兼容性评分: $compatibilityScore%" -ForegroundColor $(
    if ($compatibilityScore -ge 95) { "Green" } 
    elseif ($compatibilityScore -ge 80) { "Yellow" } 
    else { "Red" }
)

# 最终建议
Write-Host "`n🎯 Apple Business Manager集成建议" -ForegroundColor Magenta

if ($DiagnosticResults.Failed -eq 0 -and $DiagnosticResults.Warnings -eq 0) {
    Write-Host "🎉 完美！您的配置完全兼容Apple Business Manager。" -ForegroundColor Green
} elseif ($DiagnosticResults.Failed -eq 0) {
    Write-Host "✅ 良好！配置基本兼容，建议修复警告项以获得最佳体验。" -ForegroundColor Yellow
} else {
    Write-Host "⚠️  需要修复！请解决失败的测试项以确保Apple Business Manager兼容性。" -ForegroundColor Red
}

Write-Host "`n🔧 常见错误码-27480解决方案:" -ForegroundColor Cyan
Write-Host "1. 确保所有端点使用HTTPS协议" -ForegroundColor Gray
Write-Host "2. 验证issuer字段格式正确" -ForegroundColor Gray
Write-Host "3. 检查JWKS端点可访问性" -ForegroundColor Gray
Write-Host "4. 确认SSL/TLS证书有效" -ForegroundColor Gray
Write-Host "5. 验证OIDC配置包含所有必需字段" -ForegroundColor Gray

Write-Host "`n🍎 Apple Business Manager兼容性诊断完成!" -ForegroundColor Green
