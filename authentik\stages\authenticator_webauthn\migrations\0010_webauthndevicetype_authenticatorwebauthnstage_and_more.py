# Generated by Django 5.0.3 on 2024-04-02 23:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentik_flows", "0027_auto_20231028_1424"),
        ("authentik_stages_authenticator_webauthn", "0009_authenticatewebauthnstage_friendly_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="WebAuthnDeviceType",
            fields=[
                ("aaguid", models.UUIDField(primary_key=True, serialize=False, unique=True)),
                ("description", models.TextField()),
                ("icon", models.TextField(null=True)),
            ],
            options={
                "verbose_name": "WebAuthn Device type",
                "verbose_name_plural": "WebAuthn Device types",
            },
        ),
        migrations.RenameModel("AuthenticateWebAuthnStage", "AuthenticatorWebAuthnStage"),
        migrations.AddField(
            model_name="webauthndevice",
            name="device_type",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_DEFAULT,
                to="authentik_stages_authenticator_webauthn.webauthndevicetype",
            ),
        ),
        migrations.AddField(
            model_name="authenticatorwebauthnstage",
            name="device_type_restrictions",
            field=models.ManyToManyField(
                blank=True, to="authentik_stages_authenticator_webauthn.webauthndevicetype"
            ),
        ),
    ]
