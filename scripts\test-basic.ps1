# Basic Apple Business Manager OIDC Endpoint Test

param(
    [string]$BaseURL = "http://localhost:8000"
)

Write-Host "Testing Apple Business Manager OIDC Endpoints..." -ForegroundColor Green
Write-Host "Base URL: $BaseURL" -ForegroundColor Cyan

$TestResults = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$URL,
        [string]$Method = "GET"
    )
    
    $TestResults.Total++
    Write-Host "`nTesting: $Name" -ForegroundColor Yellow
    Write-Host "URL: $URL" -ForegroundColor Gray
    
    try {
        if ($Method -eq "POST") {
            $response = Invoke-WebRequest -Uri $URL -Method POST -UseBasicParsing
        } else {
            $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
        }
        
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Cyan
        
        if ($response.Headers['Content-Type'] -like "*application/json*") {
            try {
                $json = $response.Content | ConvertFrom-Json
                $fieldCount = ($json | Get-Member -MemberType NoteProperty).Count
                Write-Host "JSON Fields: $fieldCount" -ForegroundColor Cyan
            } catch {
                Write-Host "JSON Parse Failed" -ForegroundColor Yellow
            }
        }
        
        $TestResults.Passed++
        return $true
        
    } catch {
        Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
        $TestResults.Failed++
        return $false
    }
}

# Test OIDC Discovery
Test-Endpoint -Name "OIDC Discovery" -URL "$BaseURL/.well-known/openid_configuration"

# Test JWKS
Test-Endpoint -Name "JWKS Endpoint" -URL "$BaseURL/oauth2/jwks"

# Test SSF Configuration
Test-Endpoint -Name "SSF Configuration" -URL "$BaseURL/.well-known/ssf_configuration"

# Test Apple JWKS
Test-Endpoint -Name "Apple JWKS" -URL "$BaseURL/jwks"

# Test Redirect
Test-Endpoint -Name "App JWKS Redirect" -URL "$BaseURL/application/o/default/jwks/"

# Results
Write-Host "`nTest Results:" -ForegroundColor Magenta
Write-Host "Total: $($TestResults.Total)" -ForegroundColor Cyan
Write-Host "Passed: $($TestResults.Passed)" -ForegroundColor Green
Write-Host "Failed: $($TestResults.Failed)" -ForegroundColor Red

if ($TestResults.Total -gt 0) {
    $successRate = [math]::Round(($TestResults.Passed / $TestResults.Total) * 100, 2)
    Write-Host "Success Rate: $successRate%" -ForegroundColor Green
}

Write-Host "`nTesting Complete!" -ForegroundColor Green
