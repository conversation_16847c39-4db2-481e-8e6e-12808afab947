# Generated by Django 4.1.2 on 2022-10-19 19:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies", "0007_policybindingmodel_policy_engine_mode"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="policybinding",
            index=models.Index(fields=["policy"], name="authentik_p_policy__534e15_idx"),
        ),
        migrations.AddIndex(
            model_name="policybinding",
            index=models.Index(fields=["group"], name="authentik_p_group_i_5d2d24_idx"),
        ),
        migrations.AddIndex(
            model_name="policybinding",
            index=models.Index(fields=["user"], name="authentik_p_user_id_603323_idx"),
        ),
        migrations.AddIndex(
            model_name="policybinding",
            index=models.Index(fields=["target"], name="authentik_p_target__2e4d50_idx"),
        ),
    ]
