# Apple Business Manager集成指南

## 📋 概述

本指南详细说明如何将go-abm-idp作为Custom Identity Provider集成到Apple Business Manager中，实现企业用户的统一身份认证和管理。

## 🎯 功能特性

### **核心OIDC功能**
- ✅ **OpenID Connect Discovery 1.0** - 完整的OIDC配置发现
- ✅ **OAuth 2.0授权码流程** - 支持PKCE安全增强
- ✅ **JWT令牌签发** - RS256算法签名
- ✅ **用户信息端点** - 标准用户属性返回
- ✅ **JWKS端点** - 公钥分发和验证

### **Apple Business Manager专用功能**
- ✅ **用户列表同步** - 支持全量和增量同步
- ✅ **密码更改通知** - 自动通知Apple Business Manager
- ✅ **用户状态查询** - 单个和批量用户状态
- ✅ **SSF事件推送** - 安全事件实时通知
- ✅ **企业级审计日志** - 完整的操作记录

## 🔧 配置步骤

### **步骤1: Apple Business Manager配置**

1. **登录Apple Business Manager**
   - 访问 [business.apple.com](https://business.apple.com)
   - 使用管理员账户登录

2. **添加Custom Identity Provider**
   - 导航到 `Settings` > `Authentication`
   - 选择 `Add Identity Provider`
   - 选择 `Custom Identity Provider`

3. **配置OIDC发现URL**
   ```
   Discovery URL: https://your-domain.com/.well-known/openid_configuration
   ```

4. **验证配置**
   - Apple Business Manager会自动验证OIDC配置
   - 确保所有端点都可访问且返回正确格式

### **步骤2: 权限配置**

Apple Business Manager会请求以下权限来同步用户列表：

#### **标准OIDC权限**
- `openid` - 基础OpenID Connect功能
- `profile` - 用户基本信息
- `email` - 用户邮箱地址

#### **Apple Business Manager专用权限**
- `read_users` - 读取用户列表
- `read_user_profiles` - 读取用户详细信息
- `read_user_status` - 读取用户状态
- `read_user_roles` - 读取用户角色
- `read_login_history` - 读取登录历史
- `read_password_status` - 读取密码状态

### **步骤3: 用户同步配置**

#### **同步端点**
```
POST /application/o/abm/users/sync
```

#### **同步请求格式**
```json
{
  "permissions": [
    "read_users",
    "read_user_profiles",
    "read_user_status"
  ],
  "sync_type": "full",
  "requested_attributes": [
    "user_id",
    "username", 
    "email",
    "display_name",
    "status",
    "last_login_time",
    "password_changed_time"
  ],
  "limit": 100,
  "offset": 0
}
```

#### **同步响应格式**
```json
{
  "status": "success",
  "users": [
    {
      "user_id": "user_001",
      "username": "john.doe",
      "email": "<EMAIL>",
      "display_name": "John Doe",
      "status": "active",
      "last_login_time": "2025-08-04T15:30:00Z",
      "password_changed_time": "2025-08-04T10:00:00Z",
      "requires_reauth": false
    }
  ],
  "metadata": {
    "sync_timestamp": "2025-08-04T15:55:00Z",
    "total_users": 150,
    "returned_users": 100,
    "has_more": true,
    "next_offset": 100,
    "sync_type": "full"
  }
}
```

## 🔐 密码更改处理

### **自动通知机制**

当用户密码发生更改时，系统会：

1. **记录密码更改事件**
2. **触发SSF事件** - 通知Apple Business Manager
3. **更新用户状态** - 标记需要重新认证
4. **发送通知** - 通过专用端点通知

### **密码更改通知端点**
```
POST /application/o/abm/users/password-changed
```

### **通知请求格式**
```json
{
  "user_id": "user_001",
  "email": "<EMAIL>",
  "timestamp": "2025-08-04T16:00:00Z"
}
```

### **用户重新认证流程**

1. **Apple Business Manager检测到密码更改**
2. **提示用户重新登录**
3. **重定向到Identity Provider**
4. **用户输入新密码完成认证**
5. **更新认证状态**

## 🔍 用户状态查询

### **单个用户状态查询**
```
GET /application/o/abm/users/{user_id}/status
```

### **批量用户状态查询**
```
POST /application/o/abm/users/status
```

### **请求格式**
```json
{
  "user_ids": ["user_001", "user_002", "user_003"]
}
```

### **响应格式**
```json
{
  "users": [
    {
      "user_id": "user_001",
      "status": "active",
      "last_login_time": "2025-08-04T15:30:00Z",
      "password_changed_time": "2025-08-04T10:00:00Z",
      "requires_reauth": false,
      "account_locked": false,
      "login_attempts": 0
    }
  ],
  "total_count": 1,
  "timestamp": "2025-08-04T15:55:00Z"
}
```

## 📊 SSF事件集成

### **支持的SSF事件类型**

- `user_logout` - 用户登出事件
- `password_change` - 密码更改事件
- `authenticator_change` - 认证器变更事件
- `session_revoke` - 会话撤销事件

### **SSF配置端点**
```
GET /.well-known/ssf_configuration
```

### **SSF事件推送**

系统会自动向Apple Business Manager推送相关的安全事件，确保用户状态同步。

## 🛡️ 安全最佳实践

### **传输安全**
- ✅ **强制HTTPS** - 所有通信必须使用HTTPS
- ✅ **TLS 1.2+** - 使用现代TLS版本
- ✅ **证书验证** - 验证SSL/TLS证书有效性

### **认证安全**
- ✅ **PKCE支持** - 防止授权码拦截攻击
- ✅ **状态参数** - 防止CSRF攻击
- ✅ **短期令牌** - JWT令牌有效期限制
- ✅ **令牌轮换** - 支持刷新令牌机制

### **数据保护**
- ✅ **最小权限原则** - 只请求必要的用户信息
- ✅ **数据加密** - 敏感数据加密存储
- ✅ **审计日志** - 完整的操作审计记录
- ✅ **访问控制** - 基于角色的访问控制

## 🧪 测试验证

### **端点可用性测试**
```bash
# OIDC配置发现
curl -k https://your-domain.com/.well-known/openid_configuration

# JWKS端点
curl -k https://your-domain.com/oauth2/jwks

# SSF配置
curl -k https://your-domain.com/.well-known/ssf_configuration
```

### **用户同步测试**
```bash
# 用户同步请求
curl -X POST https://your-domain.com/application/o/abm/users/sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "permissions": ["read_users"],
    "sync_type": "full",
    "limit": 10
  }'
```

### **诊断工具**
```bash
# 运行Apple Business Manager兼容性诊断
./scripts/apple-diagnostic-simple.ps1 -BaseURL "https://your-domain.com"

# 运行端点路径一致性验证
./scripts/verify-endpoints-simple.ps1
```

## 🚨 故障排除

### **常见问题**

1. **错误码-27480**
   - **原因**: 端点URL不一致或HTTPS配置问题
   - **解决**: 确保所有端点使用HTTPS且路径一致

2. **用户同步失败**
   - **原因**: 权限不足或数据格式错误
   - **解决**: 检查权限配置和响应格式

3. **密码更改通知失败**
   - **原因**: SSF事件推送配置问题
   - **解决**: 验证SSF配置和事件处理器

### **日志分析**
```bash
# 查看应用日志
tail -f /var/log/go-abm-idp/app.log

# 查看系统服务日志
journalctl -u go-abm-idp -f

# 过滤Apple Business Manager相关日志
grep "Apple Business Manager" /var/log/go-abm-idp/app.log
```

## 📞 支持

### **技术支持**
- 📧 **邮箱**: <EMAIL>
- 📚 **文档**: [完整技术文档链接]
- 🐛 **问题报告**: [GitHub Issues链接]

### **Apple Business Manager支持**
- 📖 **官方文档**: [Apple Business Manager文档]
- 📞 **Apple支持**: [Apple企业支持]

## 🎉 集成完成

完成以上配置后，您的Custom Identity Provider将完全集成到Apple Business Manager中，支持：

- ✅ **统一身份认证** - 企业用户使用现有账户登录
- ✅ **自动用户同步** - 用户列表自动同步到Apple Business Manager
- ✅ **密码更改通知** - 密码更改时自动提示重新认证
- ✅ **安全事件监控** - 实时安全事件推送和监控
- ✅ **企业级管理** - 完整的用户生命周期管理

**🍎 Apple Business Manager Custom Identity Provider集成完成！**
