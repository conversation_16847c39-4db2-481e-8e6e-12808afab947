package service

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"regexp"

	"go.uber.org/zap"
)

// PKCEService 基于Authentik模式的PKCE验证服务
// 参考Authentik的TokenParams.validate_pkce实现
type PKCEService struct {
	logger *zap.Logger
}

// NewPKCEService 创建PKCE验证服务
func NewPKCEService(logger *zap.Logger) *PKCEService {
	return &PKCEService{
		logger: logger,
	}
}

// PKCEValidationRequest PKCE验证请求
type PKCEValidationRequest struct {
	CodeChallenge       string `json:"code_challenge"`
	CodeChallengeMethod string `json:"code_challenge_method"`
	CodeVerifier        string `json:"code_verifier"`
}

// PKCEValidationResult PKCE验证结果
type PKCEValidationResult struct {
	Valid   bool   `json:"valid"`
	Error   string `json:"error,omitempty"`
	Message string `json:"message,omitempty"`
}

// ValidateCodeChallenge 验证授权请求中的code_challenge参数
// 基于Authentik的OAuthAuthorizationParams.validate实现
func (s *PKCEService) ValidateCodeChallenge(codeChallenge, codeChallengeMethod string) error {
	// 如果没有提供code_challenge，则不需要PKCE验证
	if codeChallenge == "" {
		return nil
	}

	// 如果提供了code_challenge，必须提供method
	if codeChallengeMethod == "" {
		s.logger.Warn("PKCE validation failed: missing code_challenge_method")
		return fmt.Errorf("code_challenge_method is required when code_challenge is provided")
	}

	// 验证method是否支持
	if codeChallengeMethod != "plain" && codeChallengeMethod != "S256" {
		s.logger.Warn("PKCE validation failed: unsupported method",
			zap.String("method", codeChallengeMethod))
		return fmt.Errorf("unsupported code_challenge_method: %s", codeChallengeMethod)
	}

	// 验证code_challenge格式 (RFC 7636)
	if err := s.validateCodeChallengeFormat(codeChallenge); err != nil {
		s.logger.Warn("PKCE validation failed: invalid code_challenge format",
			zap.String("challenge", codeChallenge), zap.Error(err))
		return err
	}

	s.logger.Debug("PKCE code_challenge validation passed",
		zap.String("method", codeChallengeMethod),
		zap.String("challenge_length", fmt.Sprintf("%d", len(codeChallenge))))

	return nil
}

// ValidateCodeVerifier 验证令牌请求中的code_verifier参数
// 基于Authentik的TokenParams.__check_pkce实现
func (s *PKCEService) ValidateCodeVerifier(storedChallenge, storedMethod, codeVerifier string) error {
	// 如果授权码有PKCE但令牌请求没有code_verifier
	if storedChallenge != "" && codeVerifier == "" {
		s.logger.Warn("PKCE validation failed: missing code_verifier for PKCE-protected authorization code")
		return fmt.Errorf("invalid_grant: missing code_verifier")
	}

	// 如果授权码没有PKCE但令牌请求有code_verifier (防降级攻击)
	if storedChallenge == "" && codeVerifier != "" {
		s.logger.Warn("PKCE validation failed: unexpected code_verifier for non-PKCE authorization code")
		return fmt.Errorf("invalid_grant: unexpected code_verifier")
	}

	// 如果都没有PKCE参数，验证通过
	if storedChallenge == "" && codeVerifier == "" {
		return nil
	}

	// 验证code_verifier格式
	if err := s.validateCodeVerifierFormat(codeVerifier); err != nil {
		s.logger.Warn("PKCE validation failed: invalid code_verifier format",
			zap.String("verifier", codeVerifier), zap.Error(err))
		return fmt.Errorf("invalid_grant: %v", err)
	}

	// 根据方法验证code_challenge
	var computedChallenge string
	switch storedMethod {
	case "S256":
		// SHA256方法验证 (推荐)
		hash := sha256.Sum256([]byte(codeVerifier))
		computedChallenge = base64.RawURLEncoding.EncodeToString(hash[:])
	case "plain":
		// Plain方法验证 (不推荐，但向后兼容)
		computedChallenge = codeVerifier
	default:
		s.logger.Error("PKCE validation failed: invalid stored method",
			zap.String("method", storedMethod))
		return fmt.Errorf("invalid_grant: invalid code_challenge_method")
	}

	// 比较计算出的challenge和存储的challenge
	if computedChallenge != storedChallenge {
		s.logger.Warn("PKCE validation failed: code_challenge mismatch",
			zap.String("method", storedMethod),
			zap.String("expected_length", fmt.Sprintf("%d", len(storedChallenge))),
			zap.String("computed_length", fmt.Sprintf("%d", len(computedChallenge))))
		return fmt.Errorf("invalid_grant: code_challenge verification failed")
	}

	s.logger.Debug("PKCE code_verifier validation passed",
		zap.String("method", storedMethod),
		zap.String("verifier_length", fmt.Sprintf("%d", len(codeVerifier))))

	return nil
}

// validateCodeChallengeFormat 验证code_challenge格式
// 基于RFC 7636规范
func (s *PKCEService) validateCodeChallengeFormat(codeChallenge string) error {
	// 长度检查 (43-128字符)
	if len(codeChallenge) < 43 || len(codeChallenge) > 128 {
		return fmt.Errorf("code_challenge length must be between 43 and 128 characters")
	}

	// 字符集检查 (unreserved characters: A-Z a-z 0-9 - . _ ~)
	validChars := regexp.MustCompile(`^[A-Za-z0-9\-._~]+$`)
	if !validChars.MatchString(codeChallenge) {
		return fmt.Errorf("code_challenge contains invalid characters")
	}

	return nil
}

// validateCodeVerifierFormat 验证code_verifier格式
// 基于RFC 7636规范
func (s *PKCEService) validateCodeVerifierFormat(codeVerifier string) error {
	// 长度检查 (43-128字符)
	if len(codeVerifier) < 43 || len(codeVerifier) > 128 {
		return fmt.Errorf("code_verifier length must be between 43 and 128 characters")
	}

	// 字符集检查 (unreserved characters: A-Za-z 0-9 - . _ ~)
	validChars := regexp.MustCompile(`^[A-Za-z0-9\-._~]+$`)
	if !validChars.MatchString(codeVerifier) {
		return fmt.Errorf("code_verifier contains invalid characters")
	}

	return nil
}

// GenerateCodeChallenge 生成code_challenge (用于测试)
// 基于Authentik的实现模式
func (s *PKCEService) GenerateCodeChallenge(codeVerifier, method string) (string, error) {
	if err := s.validateCodeVerifierFormat(codeVerifier); err != nil {
		return "", err
	}

	switch method {
	case "S256":
		hash := sha256.Sum256([]byte(codeVerifier))
		return base64.RawURLEncoding.EncodeToString(hash[:]), nil
	case "plain":
		return codeVerifier, nil
	default:
		return "", fmt.Errorf("unsupported method: %s", method)
	}
}

// IsS256Preferred 检查是否应该优先使用S256方法
// 基于Authentik的安全最佳实践
func (s *PKCEService) IsS256Preferred() bool {
	return true // 始终推荐S256方法
}

// GetSupportedMethods 获取支持的PKCE方法
// 基于Authentik的配置
func (s *PKCEService) GetSupportedMethods() []string {
	return []string{"S256", "plain"}
}

// ValidateRequest 完整的PKCE请求验证
// 集成授权和令牌阶段的验证
func (s *PKCEService) ValidateRequest(req *PKCEValidationRequest) *PKCEValidationResult {
	// 授权阶段验证
	if req.CodeChallenge != "" {
		if err := s.ValidateCodeChallenge(req.CodeChallenge, req.CodeChallengeMethod); err != nil {
			return &PKCEValidationResult{
				Valid:   false,
				Error:   "invalid_request",
				Message: err.Error(),
			}
		}
	}

	// 令牌阶段验证
	if req.CodeVerifier != "" {
		if err := s.ValidateCodeVerifier(req.CodeChallenge, req.CodeChallengeMethod, req.CodeVerifier); err != nil {
			return &PKCEValidationResult{
				Valid:   false,
				Error:   "invalid_grant",
				Message: err.Error(),
			}
		}
	}

	return &PKCEValidationResult{
		Valid:   true,
		Message: "PKCE validation passed",
	}
}

// LogPKCEAttempt 记录PKCE验证尝试 (用于安全监控)
// 基于Authentik的事件记录模式
func (s *PKCEService) LogPKCEAttempt(clientID, method string, success bool, reason string) {
	if success {
		s.logger.Info("PKCE validation successful",
			zap.String("client_id", clientID),
			zap.String("method", method))
	} else {
		s.logger.Warn("PKCE validation failed",
			zap.String("client_id", clientID),
			zap.String("method", method),
			zap.String("reason", reason))
	}
}
