# Apple Business Manager Error Code -27480 Diagnostic Tool

param(
    [string]$BaseURL = "https://openid.akapril.in"
)

Write-Host "Apple Business Manager Error Code -27480 Diagnostic Tool" -ForegroundColor Green
Write-Host "Target URL: $BaseURL" -ForegroundColor Cyan

$Results = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-Requirement {
    param(
        [string]$TestName,
        [scriptblock]$TestScript
    )
    
    $Results.Total++
    Write-Host "`nTesting: $TestName" -ForegroundColor Yellow
    
    try {
        $result = & $TestScript
        if ($result.Success) {
            Write-Host "PASS: $($result.Message)" -ForegroundColor Green
            $Results.Passed++
        } else {
            Write-Host "FAIL: $($result.Message)" -ForegroundColor Red
            $Results.Failed++
        }
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
        $Results.Failed++
    }
}

# Test 1: HTTPS Protocol
Test-Requirement -TestName "HTTPS Protocol Check" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        $endpoints = @(
            $oidcConfig.issuer,
            $oidcConfig.authorization_endpoint,
            $oidcConfig.token_endpoint,
            $oidcConfig.userinfo_endpoint,
            $oidcConfig.jwks_uri
        )
        
        $nonHttpsEndpoints = $endpoints | Where-Object { $_ -and -not $_.StartsWith("https://") }
        
        if ($nonHttpsEndpoints.Count -eq 0) {
            return @{ Success = $true; Message = "All endpoints use HTTPS" }
        } else {
            return @{ Success = $false; Message = "Non-HTTPS endpoints found: $($nonHttpsEndpoints -join ', ')" }
        }
    } catch {
        return @{ Success = $false; Message = "Cannot fetch OIDC config: $($_.Exception.Message)" }
    }
}

# Test 2: Issuer Format
Test-Requirement -TestName "Issuer Format Validation" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        if (-not $oidcConfig.issuer) {
            return @{ Success = $false; Message = "Issuer field missing" }
        }
        
        if (-not $oidcConfig.issuer.StartsWith("https://")) {
            return @{ Success = $false; Message = "Issuer is not HTTPS URL: $($oidcConfig.issuer)" }
        }
        
        return @{ Success = $true; Message = "Issuer format correct: $($oidcConfig.issuer)" }
    } catch {
        return @{ Success = $false; Message = "Cannot validate issuer: $($_.Exception.Message)" }
    }
}

# Test 3: JWKS Accessibility
Test-Requirement -TestName "JWKS Endpoint Accessibility" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        $jwksResponse = Invoke-RestMethod -Uri $oidcConfig.jwks_uri -UseBasicParsing
        
        if (-not $jwksResponse.keys) {
            return @{ Success = $false; Message = "JWKS response missing keys field" }
        }
        
        if ($jwksResponse.keys.Count -eq 0) {
            return @{ Success = $false; Message = "No keys in JWKS" }
        }
        
        return @{ Success = $true; Message = "JWKS endpoint working, contains $($jwksResponse.keys.Count) keys" }
    } catch {
        return @{ Success = $false; Message = "JWKS endpoint failed: $($_.Exception.Message)" }
    }
}

# Test 4: Required OIDC Fields
Test-Requirement -TestName "OIDC Configuration Completeness" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        $requiredFields = @(
            "issuer",
            "authorization_endpoint", 
            "token_endpoint",
            "userinfo_endpoint",
            "jwks_uri",
            "scopes_supported",
            "response_types_supported",
            "grant_types_supported",
            "subject_types_supported",
            "id_token_signing_alg_values_supported",
            "token_endpoint_auth_methods_supported"
        )
        
        $missingFields = $requiredFields | Where-Object { -not $oidcConfig.$_ }
        
        if ($missingFields.Count -gt 0) {
            return @{ Success = $false; Message = "Missing required fields: $($missingFields -join ', ')" }
        }
        
        return @{ Success = $true; Message = "All required OIDC fields present" }
    } catch {
        return @{ Success = $false; Message = "Cannot validate OIDC config: $($_.Exception.Message)" }
    }
}

# Test 5: PKCE Support
Test-Requirement -TestName "PKCE Support Validation" -TestScript {
    try {
        $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        if (-not $oidcConfig.code_challenge_methods_supported) {
            return @{ Success = $false; Message = "Missing code_challenge_methods_supported field" }
        }
        
        if ($oidcConfig.code_challenge_methods_supported -notcontains "S256") {
            return @{ Success = $false; Message = "S256 PKCE method not supported" }
        }
        
        return @{ Success = $true; Message = "PKCE S256 method supported" }
    } catch {
        return @{ Success = $false; Message = "Cannot validate PKCE support: $($_.Exception.Message)" }
    }
}

# Test 6: Response Headers
Test-Requirement -TestName "HTTP Response Headers" -TestScript {
    try {
        $response = Invoke-WebRequest -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
        
        $contentType = $response.Headers["Content-Type"]
        if (-not $contentType -or -not $contentType.StartsWith("application/json")) {
            return @{ Success = $false; Message = "Incorrect Content-Type: $contentType" }
        }
        
        return @{ Success = $true; Message = "Response headers correct" }
    } catch {
        return @{ Success = $false; Message = "Cannot validate response headers: $($_.Exception.Message)" }
    }
}

# Output Results
Write-Host "`nApple Business Manager Compatibility Results" -ForegroundColor Magenta
Write-Host "Total Tests: $($Results.Total)" -ForegroundColor Cyan
Write-Host "Passed: $($Results.Passed)" -ForegroundColor Green
Write-Host "Failed: $($Results.Failed)" -ForegroundColor Red

$compatibilityScore = if ($Results.Total -gt 0) { 
    [math]::Round(($Results.Passed / $Results.Total) * 100, 2) 
} else { 0 }

Write-Host "Compatibility Score: $compatibilityScore%" -ForegroundColor $(
    if ($compatibilityScore -ge 95) { "Green" } 
    elseif ($compatibilityScore -ge 80) { "Yellow" } 
    else { "Red" }
)

# Final Recommendations
Write-Host "`nApple Business Manager Integration Status" -ForegroundColor Magenta

if ($Results.Failed -eq 0) {
    Write-Host "SUCCESS: Your configuration is fully compatible with Apple Business Manager!" -ForegroundColor Green
} else {
    Write-Host "ATTENTION: Please fix the failed tests to ensure Apple Business Manager compatibility." -ForegroundColor Red
}

Write-Host "`nCommon Error Code -27480 Solutions:" -ForegroundColor Cyan
Write-Host "1. Ensure all endpoints use HTTPS protocol" -ForegroundColor Gray
Write-Host "2. Verify issuer field format is correct" -ForegroundColor Gray
Write-Host "3. Check JWKS endpoint accessibility" -ForegroundColor Gray
Write-Host "4. Confirm SSL/TLS certificate is valid" -ForegroundColor Gray
Write-Host "5. Verify OIDC configuration contains all required fields" -ForegroundColor Gray

Write-Host "`nApple Business Manager Diagnostic Complete!" -ForegroundColor Green
