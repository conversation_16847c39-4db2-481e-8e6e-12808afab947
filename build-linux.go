package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

func main() {
	fmt.Println("🐧 Building Linux version of go-abm-idp...")
	
	// Create bin directory if it doesn't exist
	binDir := "bin"
	if err := os.Mkdir<PERSON>ll(binDir, 0755); err != nil {
		fmt.Printf("❌ Failed to create bin directory: %v\n", err)
		os.Exit(1)
	}
	
	// Set environment variables for Linux cross-compilation
	env := os.Environ()
	env = append(env, "GOOS=linux")
	env = append(env, "GOARCH=amd64") 
	env = append(env, "CGO_ENABLED=0")
	
	fmt.Println("🎯 Target OS: linux")
	fmt.Println("🎯 Target Architecture: amd64")
	fmt.Println("🎯 CGO Enabled: 0")
	
	// Build command
	outputFile := filepath.Join(binDir, "go-abm-idp-linux-amd64")
	cmd := exec.Command("go", "build", 
		"-ldflags=-w -s", 
		"-o", outputFile,
		"cmd/server/main.go")
	
	cmd.Env = env
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	
	fmt.Println("\n🔨 Building application...")
	start := time.Now()
	
	if err := cmd.Run(); err != nil {
		fmt.Printf("❌ Build failed: %v\n", err)
		os.Exit(1)
	}
	
	duration := time.Since(start)
	fmt.Printf("✅ Build successful in %v!\n", duration)
	
	// Check if file was created and get its size
	if info, err := os.Stat(outputFile); err == nil {
		fmt.Printf("📦 Output file: %s\n", outputFile)
		fmt.Printf("📏 File size: %.2f MB\n", float64(info.Size())/1024/1024)
		fmt.Printf("🕒 Created: %s\n", info.ModTime().Format("2006-01-02 15:04:05"))
	} else {
		fmt.Printf("⚠️  Could not get file info: %v\n", err)
	}
	
	fmt.Println("\n🎉 Linux build completed successfully!")
}
