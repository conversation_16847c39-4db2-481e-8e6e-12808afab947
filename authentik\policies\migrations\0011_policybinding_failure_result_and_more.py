# Generated by Django 4.2.5 on 2023-09-13 18:07
import authentik.lib.models
import django.db.models.deletion

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies", "0010_alter_policy_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="policybinding",
            name="failure_result",
            field=models.BooleanField(
                default=False, help_text="Result if the Policy execution fails."
            ),
        ),
        migrations.AlterField(
            model_name="policybinding",
            name="timeout",
            field=models.PositiveIntegerField(
                default=30, help_text="Timeout after which Policy execution is terminated."
            ),
        ),
        migrations.AlterField(
            model_name="policybinding",
            name="target",
            field=authentik.lib.models.InheritanceForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="bindings",
                to="authentik_policies.policybindingmodel",
            ),
        ),
    ]
