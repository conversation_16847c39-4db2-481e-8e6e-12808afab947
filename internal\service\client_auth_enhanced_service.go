package service

import (
	"encoding/base64"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ClientAuthEnhancedService 基于Authentik模式的增强客户端认证服务
// 参考Authentik的extract_client_auth和authenticate_provider实现
type ClientAuthEnhancedService struct {
	logger *zap.Logger
}

// NewClientAuthEnhancedService 创建增强客户端认证服务
func NewClientAuthEnhancedService(logger *zap.Logger) *ClientAuthEnhancedService {
	return &ClientAuthEnhancedService{
		logger: logger,
	}
}

// ClientCredentials 客户端凭证
type ClientCredentials struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Method       string `json:"method"` // "basic", "post", "jwt"
}

// ClientAuthResult 客户端认证结果
type ClientAuthResult struct {
	Success     bool              `json:"success"`
	Credentials *ClientCredentials `json:"credentials,omitempty"`
	Error       string            `json:"error,omitempty"`
	ErrorDesc   string            `json:"error_description,omitempty"`
}

// ExtractClientAuth 提取客户端认证信息
// 基于Authentik的extract_client_auth实现，支持多种认证方法
func (s *ClientAuthEnhancedService) ExtractClientAuth(c *gin.Context) *ClientAuthResult {
	// 1. 尝试HTTP Basic认证 (推荐方法)
	if authHeader := c.GetHeader("Authorization"); authHeader != "" {
		if result := s.extractBasicAuth(authHeader); result.Success {
			s.logger.Debug("Client authenticated via HTTP Basic",
				zap.String("client_id", result.Credentials.ClientID))
			return result
		}
	}

	// 2. 尝试POST参数认证 (向后兼容)
	clientID := c.PostForm("client_id")
	clientSecret := c.PostForm("client_secret")
	if clientID != "" {
		result := &ClientAuthResult{
			Success: true,
			Credentials: &ClientCredentials{
				ClientID:     clientID,
				ClientSecret: clientSecret,
				Method:       "post",
			},
		}
		s.logger.Debug("Client authenticated via POST parameters",
			zap.String("client_id", clientID))
		return result
	}

	// 3. 尝试JWT客户端断言 (企业级)
	if assertion := c.PostForm("client_assertion"); assertion != "" {
		assertionType := c.PostForm("client_assertion_type")
		if result := s.extractJWTAssertion(assertion, assertionType); result.Success {
			s.logger.Debug("Client authenticated via JWT assertion",
				zap.String("client_id", result.Credentials.ClientID))
			return result
		}
	}

	// 认证失败
	s.logger.Warn("Client authentication failed: no valid credentials provided")
	return &ClientAuthResult{
		Success:   false,
		Error:     "invalid_client",
		ErrorDesc: "Client authentication failed",
	}
}

// extractBasicAuth 提取HTTP Basic认证
// 基于Authentik的HTTP Basic认证处理
func (s *ClientAuthEnhancedService) extractBasicAuth(authHeader string) *ClientAuthResult {
	// 验证Basic认证格式
	basicRegex := regexp.MustCompile(`^Basic\s+(.+)$`)
	matches := basicRegex.FindStringSubmatch(authHeader)
	if len(matches) != 2 {
		return &ClientAuthResult{
			Success:   false,
			Error:     "invalid_client",
			ErrorDesc: "Invalid Authorization header format",
		}
	}

	// 解码Base64
	decoded, err := base64.StdEncoding.DecodeString(matches[1])
	if err != nil {
		s.logger.Warn("Failed to decode Basic auth credentials", zap.Error(err))
		return &ClientAuthResult{
			Success:   false,
			Error:     "invalid_client",
			ErrorDesc: "Invalid Base64 encoding in Authorization header",
		}
	}

	// 解析客户端ID和密钥
	credentials := string(decoded)
	parts := strings.SplitN(credentials, ":", 2)
	if len(parts) != 2 {
		s.logger.Warn("Invalid Basic auth format: missing colon separator")
		return &ClientAuthResult{
			Success:   false,
			Error:     "invalid_client",
			ErrorDesc: "Invalid credentials format",
		}
	}

	clientID := parts[0]
	clientSecret := parts[1]

	// 验证客户端ID格式
	if err := s.validateClientID(clientID); err != nil {
		s.logger.Warn("Invalid client_id format", zap.String("client_id", clientID), zap.Error(err))
		return &ClientAuthResult{
			Success:   false,
			Error:     "invalid_client",
			ErrorDesc: err.Error(),
		}
	}

	return &ClientAuthResult{
		Success: true,
		Credentials: &ClientCredentials{
			ClientID:     clientID,
			ClientSecret: clientSecret,
			Method:       "basic",
		},
	}
}

// extractJWTAssertion 提取JWT客户端断言
// 基于Authentik的JWT客户端认证实现
func (s *ClientAuthEnhancedService) extractJWTAssertion(assertion, assertionType string) *ClientAuthResult {
	// 验证断言类型
	expectedType := "urn:ietf:params:oauth:client-assertion-type:jwt-bearer"
	if assertionType != expectedType {
		s.logger.Warn("Invalid client assertion type", zap.String("type", assertionType))
		return &ClientAuthResult{
			Success:   false,
			Error:     "invalid_client",
			ErrorDesc: "Unsupported client_assertion_type",
		}
	}

	// TODO: 实现JWT验证逻辑
	// 这里需要验证JWT签名、过期时间、issuer等
	// 参考Authentik的JWT客户端认证实现

	s.logger.Debug("JWT client assertion received", zap.String("assertion_length", fmt.Sprintf("%d", len(assertion))))

	// 临时实现：从JWT中提取client_id (实际应该解析JWT)
	// 在完整实现中，应该验证JWT签名并提取claims
	return &ClientAuthResult{
		Success: false,
		Error:   "unsupported_client_authentication_method",
		ErrorDesc: "JWT client assertion not yet implemented",
	}
}

// validateClientID 验证客户端ID格式
// 基于Authentik的客户端ID验证规则
func (s *ClientAuthEnhancedService) validateClientID(clientID string) error {
	if clientID == "" {
		return fmt.Errorf("client_id cannot be empty")
	}

	// 长度限制
	if len(clientID) < 1 || len(clientID) > 255 {
		return fmt.Errorf("client_id length must be between 1 and 255 characters")
	}

	// 字符集验证 (允许字母、数字、连字符、下划线)
	validChars := regexp.MustCompile(`^[A-Za-z0-9\-_]+$`)
	if !validChars.MatchString(clientID) {
		return fmt.Errorf("client_id contains invalid characters")
	}

	return nil
}

// AuthenticateClient 验证客户端凭证
// 基于Authentik的authenticate_provider实现
func (s *ClientAuthEnhancedService) AuthenticateClient(credentials *ClientCredentials) *ClientAuthResult {
	// 验证客户端ID
	if err := s.validateClientID(credentials.ClientID); err != nil {
		return &ClientAuthResult{
			Success:   false,
			Error:     "invalid_client",
			ErrorDesc: err.Error(),
		}
	}

	// TODO: 实现实际的客户端验证逻辑
	// 这里应该查询数据库验证客户端ID和密钥
	// 参考Authentik的OAuth2Provider模型验证

	// 临时实现：允许特定的测试客户端
	if s.isValidTestClient(credentials.ClientID, credentials.ClientSecret) {
		s.logger.Info("Client authentication successful",
			zap.String("client_id", credentials.ClientID),
			zap.String("method", credentials.Method))
		return &ClientAuthResult{
			Success:     true,
			Credentials: credentials,
		}
	}

	s.logger.Warn("Client authentication failed: invalid credentials",
		zap.String("client_id", credentials.ClientID),
		zap.String("method", credentials.Method))
	return &ClientAuthResult{
		Success:   false,
		Error:     "invalid_client",
		ErrorDesc: "Invalid client credentials",
	}
}

// isValidTestClient 检查是否为有效的测试客户端
// 临时实现，实际应该查询数据库
func (s *ClientAuthEnhancedService) isValidTestClient(clientID, clientSecret string) bool {
	// Apple Business Manager测试客户端
	testClients := map[string]string{
		"apple-business-manager": "test-secret-key",
		"abm-test-client":       "abm-test-secret",
		"test-client":           "test-secret",
	}

	expectedSecret, exists := testClients[clientID]
	return exists && expectedSecret == clientSecret
}

// GetSupportedAuthMethods 获取支持的客户端认证方法
// 基于Authentik的配置
func (s *ClientAuthEnhancedService) GetSupportedAuthMethods() []string {
	return []string{
		"client_secret_basic",
		"client_secret_post",
		// "client_secret_jwt",     // 未来实现
		// "private_key_jwt",       // 未来实现
	}
}

// ValidateAuthMethod 验证认证方法是否支持
func (s *ClientAuthEnhancedService) ValidateAuthMethod(method string) bool {
	supportedMethods := s.GetSupportedAuthMethods()
	for _, supported := range supportedMethods {
		if method == supported {
			return true
		}
	}
	return false
}

// LogAuthAttempt 记录认证尝试 (用于安全监控)
// 基于Authentik的事件记录模式
func (s *ClientAuthEnhancedService) LogAuthAttempt(clientID, method string, success bool, reason string) {
	if success {
		s.logger.Info("Client authentication successful",
			zap.String("client_id", clientID),
			zap.String("method", method),
			zap.Time("timestamp", time.Now()))
	} else {
		s.logger.Warn("Client authentication failed",
			zap.String("client_id", clientID),
			zap.String("method", method),
			zap.String("reason", reason),
			zap.Time("timestamp", time.Now()))
	}
}

// SetSecurityHeaders 设置安全响应头
// 基于Authentik的安全最佳实践
func (s *ClientAuthEnhancedService) SetSecurityHeaders(c *gin.Context) {
	c.Header("Cache-Control", "no-store")
	c.Header("Pragma", "no-cache")
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "DENY")
	c.Header("X-XSS-Protection", "1; mode=block")
}
