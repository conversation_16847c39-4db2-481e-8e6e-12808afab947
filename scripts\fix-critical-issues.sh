#!/bin/bash

# Go ABM IDP 关键问题修复脚本
# 用于修复代码质量、安全和性能问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Go ABM IDP 关键问题修复脚本 ===${NC}"
echo ""

# 1. 代码质量检查和修复
echo -e "${YELLOW}1. 代码质量检查和修复${NC}"

# 检查Go版本
echo -n "检查Go版本... "
if ! command -v go &> /dev/null; then
    echo -e "${RED}✗ Go未安装${NC}"
    exit 1
fi
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo -e "${GREEN}✓ Go $GO_VERSION${NC}"

# 格式化代码
echo -n "格式化Go代码... "
if go fmt ./...; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗${NC}"
fi

# 检查未使用的导入
echo -n "检查未使用的导入... "
if command -v goimports &> /dev/null; then
    goimports -w .
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ goimports未安装，跳过${NC}"
fi

# 运行go vet
echo -n "运行go vet检查... "
if go vet ./...; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗ 发现问题，请检查输出${NC}"
fi

# 运行golint（如果可用）
echo -n "运行golint检查... "
if command -v golint &> /dev/null; then
    golint ./... | head -20
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ golint未安装，跳过${NC}"
fi

echo ""

# 2. 安全问题修复
echo -e "${YELLOW}2. 安全问题修复${NC}"

# 检查敏感信息
echo -n "检查敏感信息泄露... "
SENSITIVE_PATTERNS=(
    "password.*=.*['\"][^'\"]*['\"]"
    "secret.*=.*['\"][^'\"]*['\"]"
    "token.*=.*['\"][^'\"]*['\"]"
    "key.*=.*['\"][^'\"]*['\"]"
)

FOUND_SENSITIVE=false
for pattern in "${SENSITIVE_PATTERNS[@]}"; do
    if grep -r -i -E "$pattern" --include="*.go" --include="*.yaml" --include="*.yml" . 2>/dev/null | grep -v "example" | grep -v "test" | head -5; then
        FOUND_SENSITIVE=true
    fi
done

if [ "$FOUND_SENSITIVE" = true ]; then
    echo -e "${RED}✗ 发现潜在的敏感信息${NC}"
else
    echo -e "${GREEN}✓${NC}"
fi

# 检查SQL注入风险
echo -n "检查SQL注入风险... "
if grep -r "fmt.Sprintf.*SELECT\|fmt.Sprintf.*INSERT\|fmt.Sprintf.*UPDATE\|fmt.Sprintf.*DELETE" --include="*.go" . 2>/dev/null | head -5; then
    echo -e "${RED}✗ 发现潜在的SQL注入风险${NC}"
else
    echo -e "${GREEN}✓${NC}"
fi

# 检查不安全的随机数生成
echo -n "检查不安全的随机数生成... "
if grep -r "math/rand" --include="*.go" . 2>/dev/null | grep -v "crypto/rand" | head -5; then
    echo -e "${YELLOW}⚠ 发现使用math/rand，建议使用crypto/rand${NC}"
else
    echo -e "${GREEN}✓${NC}"
fi

echo ""

# 3. 性能问题修复
echo -e "${YELLOW}3. 性能问题检查${NC}"

# 检查潜在的内存泄漏
echo -n "检查潜在的内存泄漏... "
GOROUTINE_LEAKS=$(grep -r "go func()" --include="*.go" . 2>/dev/null | wc -l)
if [ "$GOROUTINE_LEAKS" -gt 10 ]; then
    echo -e "${YELLOW}⚠ 发现 $GOROUTINE_LEAKS 个匿名goroutine，请检查是否有泄漏风险${NC}"
else
    echo -e "${GREEN}✓${NC}"
fi

# 检查数据库连接管理
echo -n "检查数据库连接管理... "
if grep -r "db\.Exec\|db\.Query" --include="*.go" . 2>/dev/null | grep -v "WithContext" | head -3; then
    echo -e "${YELLOW}⚠ 发现未使用Context的数据库操作${NC}"
else
    echo -e "${GREEN}✓${NC}"
fi

# 检查缓存实现
echo -n "检查缓存实现... "
if grep -r "sync\.Map\|sync\.RWMutex" --include="*.go" . 2>/dev/null | wc -l | grep -q "0"; then
    echo -e "${YELLOW}⚠ 未发现并发安全的缓存实现${NC}"
else
    echo -e "${GREEN}✓${NC}"
fi

echo ""

# 4. 依赖检查和更新
echo -e "${YELLOW}4. 依赖检查和更新${NC}"

# 检查go.mod
echo -n "检查go.mod文件... "
if [ -f "go.mod" ]; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗ go.mod文件不存在${NC}"
    exit 1
fi

# 检查依赖漏洞
echo -n "检查依赖漏洞... "
if command -v govulncheck &> /dev/null; then
    if govulncheck ./...; then
        echo -e "${GREEN}✓${NC}"
    else
        echo -e "${RED}✗ 发现安全漏洞${NC}"
    fi
else
    echo -e "${YELLOW}⚠ govulncheck未安装，跳过${NC}"
fi

# 整理依赖
echo -n "整理依赖... "
if go mod tidy; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗${NC}"
fi

echo ""

# 5. 测试运行
echo -e "${YELLOW}5. 测试运行${NC}"

# 运行单元测试
echo -n "运行单元测试... "
if go test -short ./...; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${RED}✗ 测试失败${NC}"
fi

# 检查测试覆盖率
echo -n "检查测试覆盖率... "
if go test -cover ./... | tail -1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ 无法获取覆盖率${NC}"
fi

echo ""

# 6. 构建检查
echo -e "${YELLOW}6. 构建检查${NC}"

# 检查构建
echo -n "检查构建... "
if go build -o /tmp/go-abm-idp ./cmd/server/main.go; then
    echo -e "${GREEN}✓${NC}"
    rm -f /tmp/go-abm-idp
else
    echo -e "${RED}✗ 构建失败${NC}"
fi

# 检查交叉编译
echo -n "检查交叉编译... "
if GOOS=linux GOARCH=amd64 go build -o /tmp/go-abm-idp-linux ./cmd/server/main.go; then
    echo -e "${GREEN}✓${NC}"
    rm -f /tmp/go-abm-idp-linux
else
    echo -e "${RED}✗ 交叉编译失败${NC}"
fi

echo ""

# 7. 配置文件检查
echo -e "${YELLOW}7. 配置文件检查${NC}"

# 检查配置文件
echo -n "检查配置文件... "
CONFIG_FILES=("config/config.yaml" "config/config.example.yaml")
for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        echo -n "✓ $config_file "
    else
        echo -n "✗ $config_file "
    fi
done
echo ""

# 检查Docker文件
echo -n "检查Docker文件... "
if [ -f "Dockerfile" ]; then
    echo -e "${GREEN}✓ Dockerfile存在${NC}"
else
    echo -e "${YELLOW}⚠ Dockerfile不存在${NC}"
fi

if [ -f "docker-compose.yml" ]; then
    echo -e "${GREEN}✓ docker-compose.yml存在${NC}"
else
    echo -e "${YELLOW}⚠ docker-compose.yml不存在${NC}"
fi

echo ""

# 8. 文档检查
echo -e "${YELLOW}8. 文档检查${NC}"

# 检查必要的文档
REQUIRED_DOCS=("README.md" "QUICKSTART.md" "docs/README.md")
for doc in "${REQUIRED_DOCS[@]}"; do
    echo -n "检查 $doc... "
    if [ -f "$doc" ]; then
        echo -e "${GREEN}✓${NC}"
    else
        echo -e "${RED}✗${NC}"
    fi
done

echo ""

# 9. 生成修复报告
echo -e "${YELLOW}9. 生成修复报告${NC}"

REPORT_FILE="fix-report-$(date +%Y%m%d-%H%M%S).md"
cat > "$REPORT_FILE" << EOF
# Go ABM IDP 修复报告

**生成时间**: $(date)
**Go版本**: $GO_VERSION

## 修复项目

### 代码质量
- [x] 代码格式化
- [x] 导入整理
- [x] 静态分析检查

### 安全检查
- [x] 敏感信息检查
- [x] SQL注入风险检查
- [x] 随机数生成检查

### 性能检查
- [x] 内存泄漏检查
- [x] 数据库连接检查
- [x] 缓存实现检查

### 依赖管理
- [x] 依赖整理
- [x] 漏洞检查

### 测试和构建
- [x] 单元测试
- [x] 构建检查
- [x] 交叉编译检查

## 建议的后续行动

1. 修复发现的安全问题
2. 优化性能瓶颈
3. 增加测试覆盖率
4. 完善文档

## 详细问题

请查看上述输出中标记为 ✗ 或 ⚠ 的项目。

EOF

echo -e "${GREEN}修复报告已生成: $REPORT_FILE${NC}"

echo ""
echo -e "${BLUE}=== 修复脚本执行完成 ===${NC}"
echo ""
echo -e "${GREEN}✓ 表示检查通过${NC}"
echo -e "${YELLOW}⚠ 表示需要注意${NC}"
echo -e "${RED}✗ 表示需要修复${NC}"
echo ""
echo "请根据上述输出修复发现的问题，然后重新运行此脚本进行验证。"
