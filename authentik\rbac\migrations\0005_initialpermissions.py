# Generated by Django 5.0.13 on 2025-04-07 13:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("authentik_rbac", "0004_alter_systempermission_options"),
    ]

    operations = [
        migrations.CreateModel(
            name="InitialPermissions",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("name", models.TextField(max_length=150, unique=True)),
                ("mode", models.CharField(choices=[("user", "User"), ("role", "Role")])),
                ("permissions", models.ManyToManyField(blank=True, to="auth.permission")),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="authentik_rbac.role"
                    ),
                ),
            ],
            options={
                "verbose_name": "Initial Permissions",
                "verbose_name_plural": "Initial Permissions",
            },
        ),
    ]
