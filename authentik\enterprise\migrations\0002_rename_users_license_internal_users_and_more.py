# Generated by Django 4.2.4 on 2023-08-23 10:06

import django.contrib.postgres.indexes
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_enterprise", "0001_initial"),
    ]

    operations = [
        migrations.RenameField(
            model_name="license",
            old_name="users",
            new_name="internal_users",
        ),
        migrations.AlterField(
            model_name="license",
            name="key",
            field=models.TextField(),
        ),
        migrations.AddIndex(
            model_name="license",
            index=django.contrib.postgres.indexes.HashIndex(
                fields=["key"], name="authentik_e_key_523e13_hash"
            ),
        ),
        migrations.AlterModelOptions(
            name="licenseusage",
            options={
                "verbose_name": "License Usage",
                "verbose_name_plural": "License Usage Records",
            },
        ),
        migrations.AlterModelOptions(
            name="license",
            options={"verbose_name": "License", "verbose_name_plural": "Licenses"},
        ),
    ]
