# Generated by Django 3.2.3 on 2021-05-29 20:46

from django.apps.registry import Apps
from django.db import migrations, models
from django.db.backends.base.schema import BaseDatabaseSchemaEditor


def migrate_to_username(apps: Apps, schema_editor: BaseDatabaseSchemaEditor):
    db_alias = schema_editor.connection.alias

    UserReputation = apps.get_model("authentik_policies_reputation", "userreputation")
    for rep in UserReputation.objects.using(db_alias).all():
        rep.username = rep.user.username
        rep.save()


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies_reputation", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="userreputation",
            name="username",
            field=models.TextField(default=""),
            preserve_default=False,
        ),
        migrations.RunPython(migrate_to_username),
        migrations.RemoveField(
            model_name="userreputation",
            name="user",
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="userreputation",
            name="username",
            field=models.TextField(),
        ),
    ]
