# 简化的Apple Business Manager OIDC端点测试脚本

param(
    [string]$BaseURL = "http://localhost:8000"
)

Write-Host "🔍 Apple Business Manager OIDC端点验证开始..." -ForegroundColor Green
Write-Host "基础URL: $BaseURL" -ForegroundColor Cyan

$TestResults = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-SimpleEndpoint {
    param(
        [string]$Name,
        [string]$URL,
        [string]$Method = "GET"
    )
    
    $TestResults.Total++
    Write-Host "`n📋 测试: $Name" -ForegroundColor Yellow
    Write-Host "   URL: $URL" -ForegroundColor Gray
    
    try {
        if ($Method -eq "POST") {
            $response = Invoke-WebRequest -Uri $URL -Method POST -UseBasicParsing
        } else {
            $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
        }
        
        Write-Host "   ✅ 状态码: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "   📄 Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Cyan
        
        if ($response.Headers['Content-Type'] -like "*application/json*") {
            try {
                $json = $response.Content | ConvertFrom-Json
                $fieldCount = ($json | Get-Member -MemberType NoteProperty).Count
                Write-Host "   📊 JSON字段数: $fieldCount" -ForegroundColor Cyan
            } catch {
                Write-Host "   ⚠️  JSON解析失败" -ForegroundColor Yellow
            }
        }
        
        $TestResults.Passed++
        return $true
        
    } catch {
        Write-Host "   ❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
        $TestResults.Failed++
        return $false
    }
}

# 1. 测试OIDC配置发现端点
Test-SimpleEndpoint -Name "OIDC配置发现" -URL "$BaseURL/.well-known/openid_configuration"

# 2. 测试JWKS端点
Test-SimpleEndpoint -Name "JWKS端点" -URL "$BaseURL/oauth2/jwks"

# 3. 测试SSF配置端点
Test-SimpleEndpoint -Name "SSF配置发现" -URL "$BaseURL/.well-known/ssf_configuration"

# 4. 测试Apple风格的JWKS端点
Test-SimpleEndpoint -Name "Apple JWKS端点" -URL "$BaseURL/jwks"

# 5. 测试兼容性重定向
Test-SimpleEndpoint -Name "应用JWKS重定向" -URL "$BaseURL/application/o/default/jwks/"

# 6. 测试OAuth2端点 (预期失败)
Write-Host "`n🔧 OAuth2端点测试 (预期返回错误)" -ForegroundColor Magenta
Test-SimpleEndpoint -Name "OAuth2授权端点 (无参数)" -URL "$BaseURL/oauth2/authorize"
Test-SimpleEndpoint -Name "OAuth2令牌端点 (无参数)" -URL "$BaseURL/oauth2/token" -Method "POST"

# 输出结果
Write-Host "`n📊 测试结果统计" -ForegroundColor Magenta
Write-Host "总测试数: $($TestResults.Total)" -ForegroundColor Cyan
Write-Host "通过: $($TestResults.Passed)" -ForegroundColor Green
Write-Host "失败: $($TestResults.Failed)" -ForegroundColor Red

if ($TestResults.Total -gt 0) {
    $successRate = [math]::Round(($TestResults.Passed / $TestResults.Total) * 100, 2)
    Write-Host "成功率: $successRate%" -ForegroundColor Green
}

Write-Host "`n🔍 测试完成!" -ForegroundColor Green
