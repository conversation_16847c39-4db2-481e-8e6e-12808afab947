# Generated by Django 4.1.7 on 2023-06-21 12:45

from django.apps.registry import Apps
from django.db import migrations, models
from django.db.backends.base.schema import BaseDatabaseSchemaEditor


def replace_defaults(apps: Apps, schema_editor: BaseDatabaseSchemaEditor):
    db_alias = schema_editor.connection.alias

    eventmatcherpolicy = apps.get_model("authentik_policies_event_matcher", "eventmatcherpolicy")
    for policy in eventmatcherpolicy.objects.using(db_alias).all():
        changed = False
        if policy.action == "":
            policy.action = None
            changed = True
        if policy.app == "":
            policy.app = None
            changed = True
        if policy.client_ip == "":
            policy.client_ip = None
            changed = True
        if policy.model == "":
            policy.model = None
            changed = True
        if not changed:
            continue
        policy.save()


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_policies_event_matcher", "0022_eventmatcherpolicy_model"),
    ]

    operations = [
        migrations.AlterField(
            model_name="eventmatcherpolicy",
            name="action",
            field=models.TextField(
                choices=[
                    ("login", "Login"),
                    ("login_failed", "Login Failed"),
                    ("logout", "Logout"),
                    ("user_write", "User Write"),
                    ("suspicious_request", "Suspicious Request"),
                    ("password_set", "Password Set"),
                    ("secret_view", "Secret View"),
                    ("secret_rotate", "Secret Rotate"),
                    ("invitation_used", "Invite Used"),
                    ("authorize_application", "Authorize Application"),
                    ("source_linked", "Source Linked"),
                    ("impersonation_started", "Impersonation Started"),
                    ("impersonation_ended", "Impersonation Ended"),
                    ("flow_execution", "Flow Execution"),
                    ("policy_execution", "Policy Execution"),
                    ("policy_exception", "Policy Exception"),
                    ("property_mapping_exception", "Property Mapping Exception"),
                    ("system_task_execution", "System Task Execution"),
                    ("system_task_exception", "System Task Exception"),
                    ("system_exception", "System Exception"),
                    ("configuration_error", "Configuration Error"),
                    ("model_created", "Model Created"),
                    ("model_updated", "Model Updated"),
                    ("model_deleted", "Model Deleted"),
                    ("email_sent", "Email Sent"),
                    ("update_available", "Update Available"),
                    ("custom_", "Custom Prefix"),
                ],
                default=None,
                help_text="Match created events with this action type. When left empty, all action types will be matched.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="eventmatcherpolicy",
            name="app",
            field=models.TextField(
                default=None,
                help_text="Match events created by selected application. When left empty, all applications are matched.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="eventmatcherpolicy",
            name="client_ip",
            field=models.TextField(
                default=None,
                help_text="Matches Event's Client IP (strict matching, for network matching use an Expression Policy)",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="eventmatcherpolicy",
            name="model",
            field=models.TextField(
                default=None,
                help_text="Match events created by selected model. When left empty, all models are matched. When an app is selected, all the application's models are matched.",
                null=True,
            ),
        ),
        migrations.RunPython(replace_defaults),
    ]
