# Apple Business Manager OIDC端点路径一致性验证工具

param(
    [string]$BaseURL = "https://openid.akapril.in"
)

Write-Host "🔍 Apple Business Manager OIDC端点路径一致性验证" -ForegroundColor Green
Write-Host "目标URL: $BaseURL" -ForegroundColor Cyan

$Results = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-EndpointPath {
    param(
        [string]$EndpointName,
        [string]$DeclaredPath,
        [string]$Method = "GET"
    )
    
    $Results.Total++
    Write-Host "`n🔍 测试端点: $EndpointName" -ForegroundColor Yellow
    Write-Host "   声明路径: $DeclaredPath" -ForegroundColor Gray
    Write-Host "   HTTP方法: $Method" -ForegroundColor Gray
    
    try {
        $fullURL = "$BaseURL$DeclaredPath"
        
        if ($Method -eq "GET") {
            $response = Invoke-WebRequest -Uri $fullURL -Method GET -UseBasicParsing -ErrorAction Stop
        } else {
            # 对于POST端点，我们只检查是否返回405 Method Not Allowed或其他非404错误
            try {
                $response = Invoke-WebRequest -Uri $fullURL -Method $Method -UseBasicParsing -ErrorAction Stop
            } catch {
                if ($_.Exception.Response.StatusCode -eq 405) {
                    # 405 Method Not Allowed 说明端点存在但方法不对，这是预期的
                    Write-Host "   ✅ 端点存在 (405 Method Not Allowed - 预期行为)" -ForegroundColor Green
                    $Results.Passed++
                    return
                } elseif ($_.Exception.Response.StatusCode -eq 400) {
                    # 400 Bad Request 说明端点存在但参数不对，这也是预期的
                    Write-Host "   ✅ 端点存在 (400 Bad Request - 预期行为)" -ForegroundColor Green
                    $Results.Passed++
                    return
                } else {
                    throw
                }
            }
        }
        
        Write-Host "   ✅ 端点可访问 (状态码: $($response.StatusCode))" -ForegroundColor Green
        Write-Host "   📋 Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Gray
        $Results.Passed++
        
    } catch {
        if ($_.Exception.Response.StatusCode -eq 404) {
            Write-Host "   ❌ 端点不存在 (404 Not Found)" -ForegroundColor Red
            Write-Host "   💡 检查路由配置是否正确" -ForegroundColor Cyan
        } else {
            Write-Host "   ⚠️  端点访问异常: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-Host "   💡 可能是认证或参数问题，但端点路径存在" -ForegroundColor Cyan
        }
        $Results.Failed++
    }
}

# 获取OIDC配置
Write-Host "`n📋 获取OIDC配置..." -ForegroundColor Blue
try {
    $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
    Write-Host "✅ OIDC配置获取成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法获取OIDC配置: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 提取端点路径
$endpoints = @{
    "Authorization Endpoint" = @{
        "path" = $oidcConfig.authorization_endpoint -replace "^https?://[^/]+", ""
        "method" = "GET"
    }
    "Token Endpoint" = @{
        "path" = $oidcConfig.token_endpoint -replace "^https?://[^/]+", ""
        "method" = "POST"
    }
    "UserInfo Endpoint" = @{
        "path" = $oidcConfig.userinfo_endpoint -replace "^https?://[^/]+", ""
        "method" = "GET"
    }
    "JWKS Endpoint" = @{
        "path" = $oidcConfig.jwks_uri -replace "^https?://[^/]+", ""
        "method" = "GET"
    }
}

# 检查额外端点（如果存在）
if ($oidcConfig._endpoint_methods) {
    if ($oidcConfig._endpoint_methods.revocation_endpoint) {
        $endpoints["Revocation Endpoint"] = @{
            "path" = $oidcConfig._endpoint_methods.revocation_endpoint -replace "^https?://[^/]+", ""
            "method" = "POST"
        }
    }
    if ($oidcConfig._endpoint_methods.introspection_endpoint) {
        $endpoints["Introspection Endpoint"] = @{
            "path" = $oidcConfig._endpoint_methods.introspection_endpoint -replace "^https?://[^/]+", ""
            "method" = "POST"
        }
    }
}

# 测试每个端点
Write-Host "`n🧪 开始端点路径验证..." -ForegroundColor Blue

foreach ($endpointName in $endpoints.Keys) {
    $endpoint = $endpoints[$endpointName]
    Test-EndpointPath -EndpointName $endpointName -DeclaredPath $endpoint.path -Method $endpoint.method
}

# 额外测试：检查根路径JWKS重定向
Write-Host "`n🔄 测试根路径JWKS重定向..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "$BaseURL/jwks" -UseBasicParsing -MaximumRedirection 0 -ErrorAction Stop
    Write-Host "   ❌ 根路径JWKS没有重定向 (状态码: $($response.StatusCode))" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 301 -or $_.Exception.Response.StatusCode -eq 302) {
        $location = $_.Exception.Response.Headers.Location
        Write-Host "   ✅ 根路径JWKS正确重定向到: $location" -ForegroundColor Green
        $Results.Passed++
    } else {
        Write-Host "   ❌ 根路径JWKS重定向异常: $($_.Exception.Message)" -ForegroundColor Red
        $Results.Failed++
    }
    $Results.Total++
}

# 输出结果
Write-Host "`n📊 端点路径一致性验证结果" -ForegroundColor Magenta
Write-Host "总测试数: $($Results.Total)" -ForegroundColor Cyan
Write-Host "通过: $($Results.Passed)" -ForegroundColor Green
Write-Host "失败: $($Results.Failed)" -ForegroundColor Red

$consistencyScore = if ($Results.Total -gt 0) { 
    [math]::Round(($Results.Passed / $Results.Total) * 100, 2) 
} else { 0 }

Write-Host "一致性评分: $consistencyScore%" -ForegroundColor $(
    if ($consistencyScore -ge 95) { "Green" } 
    elseif ($consistencyScore -ge 80) { "Yellow" } 
    else { "Red" }
)

# 显示OIDC配置中的端点信息
Write-Host "`n📋 OIDC配置中声明的端点:" -ForegroundColor Magenta
Write-Host "Issuer: $($oidcConfig.issuer)" -ForegroundColor Gray
Write-Host "Authorization: $($oidcConfig.authorization_endpoint)" -ForegroundColor Gray
Write-Host "Token: $($oidcConfig.token_endpoint)" -ForegroundColor Gray
Write-Host "UserInfo: $($oidcConfig.userinfo_endpoint)" -ForegroundColor Gray
Write-Host "JWKS: $($oidcConfig.jwks_uri)" -ForegroundColor Gray

if ($oidcConfig._endpoint_methods) {
    Write-Host "`n🔧 额外端点信息:" -ForegroundColor Magenta
    if ($oidcConfig._endpoint_methods.revocation_endpoint) {
        Write-Host "Revocation: $($oidcConfig._endpoint_methods.revocation_endpoint)" -ForegroundColor Gray
    }
    if ($oidcConfig._endpoint_methods.introspection_endpoint) {
        Write-Host "Introspection: $($oidcConfig._endpoint_methods.introspection_endpoint)" -ForegroundColor Gray
    }
}

# 最终建议
Write-Host "`n🎯 端点路径一致性状态" -ForegroundColor Magenta

if ($Results.Failed -eq 0) {
    Write-Host "🎉 完美！所有端点路径都与OIDC配置声明一致。" -ForegroundColor Green
} else {
    Write-Host "⚠️  发现路径不一致问题，请检查路由配置。" -ForegroundColor Red
}

Write-Host "`n💡 Apple Business Manager集成建议:" -ForegroundColor Cyan
Write-Host "1. 确保所有端点路径与OIDC配置完全一致" -ForegroundColor Gray
Write-Host "2. 验证HTTP方法支持正确" -ForegroundColor Gray
Write-Host "3. 检查重定向配置是否正常工作" -ForegroundColor Gray
Write-Host "4. 确认SSL证书配置正确" -ForegroundColor Gray

Write-Host "`n🔍 端点路径一致性验证完成!" -ForegroundColor Green
