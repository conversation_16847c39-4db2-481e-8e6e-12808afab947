# Generated by Django 5.0.11 on 2025-02-05 16:20

import authentik.lib.utils.time
import django.contrib.postgres.fields
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentik_core", "0042_authenticatedsession_authentik_c_expires_08251d_idx_and_more"),
        ("authentik_crypto", "0004_alter_certificatekeypair_name"),
        ("authentik_providers_oauth2", "0027_accesstoken_authentik_p_expires_9f24a5_idx_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SSFProvider",
            fields=[
                (
                    "provider_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_core.provider",
                    ),
                ),
                (
                    "event_retention",
                    models.TextField(
                        default="days=30",
                        validators=[authentik.lib.utils.time.timedelta_string_validator],
                    ),
                ),
                (
                    "oidc_auth_providers",
                    models.ManyToManyField(
                        blank=True, default=None, to="authentik_providers_oauth2.oauth2provider"
                    ),
                ),
                (
                    "signing_key",
                    models.ForeignKey(
                        help_text="Key used to sign the SSF Events.",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_crypto.certificatekeypair",
                        verbose_name="Signing Key",
                    ),
                ),
                (
                    "token",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_core.token",
                    ),
                ),
            ],
            options={
                "verbose_name": "Shared Signals Framework Provider",
                "verbose_name_plural": "Shared Signals Framework Providers",
                "permissions": [("add_stream", "Add stream to SSF provider")],
            },
            bases=("authentik_core.provider",),
        ),
        migrations.CreateModel(
            name="Stream",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    "delivery_method",
                    models.TextField(
                        choices=[
                            (
                                "https://schemas.openid.net/secevent/risc/delivery-method/push",
                                "Risc Push",
                            ),
                            (
                                "https://schemas.openid.net/secevent/risc/delivery-method/poll",
                                "Risc Poll",
                            ),
                        ]
                    ),
                ),
                ("endpoint_url", models.TextField(null=True)),
                (
                    "events_requested",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(
                            choices=[
                                (
                                    "https://schemas.openid.net/secevent/caep/event-type/session-revoked",
                                    "Caep Session Revoked",
                                ),
                                (
                                    "https://schemas.openid.net/secevent/caep/event-type/credential-change",
                                    "Caep Credential Change",
                                ),
                                (
                                    "https://schemas.openid.net/secevent/ssf/event-type/verification",
                                    "Set Verification",
                                ),
                            ]
                        ),
                        default=list,
                        size=None,
                    ),
                ),
                ("format", models.TextField()),
                (
                    "aud",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.TextField(), default=list, size=None
                    ),
                ),
                ("iss", models.TextField()),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_providers_ssf.ssfprovider",
                    ),
                ),
            ],
            options={
                "verbose_name": "SSF Stream",
                "verbose_name_plural": "SSF Streams",
                "default_permissions": ["change", "delete", "view"],
            },
        ),
        migrations.CreateModel(
            name="StreamEvent",
            fields=[
                ("created", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("expires", models.DateTimeField(default=None, null=True)),
                ("expiring", models.BooleanField(default=True)),
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    "status",
                    models.TextField(
                        choices=[
                            ("pending_new", "Pending New"),
                            ("pending_failed", "Pending Failed"),
                            ("sent", "Sent"),
                        ]
                    ),
                ),
                (
                    "type",
                    models.TextField(
                        choices=[
                            (
                                "https://schemas.openid.net/secevent/caep/event-type/session-revoked",
                                "Caep Session Revoked",
                            ),
                            (
                                "https://schemas.openid.net/secevent/caep/event-type/credential-change",
                                "Caep Credential Change",
                            ),
                            (
                                "https://schemas.openid.net/secevent/ssf/event-type/verification",
                                "Set Verification",
                            ),
                        ]
                    ),
                ),
                ("payload", models.JSONField(default=dict)),
                (
                    "stream",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authentik_providers_ssf.stream",
                    ),
                ),
            ],
            options={
                "verbose_name": "SSF Stream Event",
                "verbose_name_plural": "SSF Stream Events",
                "ordering": ("-created",),
            },
        ),
    ]
