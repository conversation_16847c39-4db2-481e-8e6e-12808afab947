-- 创建Apple Business Manager OAuth2客户端配置
-- 解决重定向URI验证错误：https://gsa-ws.apple.com/grandslam/GsService2/acs

-- 删除已存在的Apple Business Manager客户端（如果有）
DELETE FROM oauth2_providers WHERE client_id = 'apple-business-manager';
DELETE FROM oauth2_providers WHERE client_id = 'go-abm-idp-client';

-- 创建Apple Business Manager客户端
INSERT INTO oauth2_providers (
    name,
    client_id,
    client_secret,
    client_type,
    description,
    redirect_uris,
    grant_types,
    response_types,
    scopes,
    access_token_validity,
    refresh_token_validity,
    auth_code_validity,
    status,
    created_at,
    updated_at
) VALUES (
    'Apple Business Manager',
    'apple-business-manager',
    'apple-client-secret',
    'confidential',
    'Apple Business Manager企业身份管理集成',
    '["https://gsa-ws.apple.com/grandslam/GsService2/acs"]',
    '["authorization_code", "refresh_token"]',
    '["code"]',
    '["openid", "profile", "email", "ssf.manage", "ssf.read", "offline_access"]',
    3600,      -- 1小时访问令牌
    2592000,   -- 30天刷新令牌
    600,       -- 10分钟授权码
    'active',
    NOW(),
    NOW()
);

-- 创建备用的Apple Business Manager客户端（使用go-abm-idp-client ID）
INSERT INTO oauth2_providers (
    name,
    client_id,
    client_secret,
    client_type,
    description,
    redirect_uris,
    grant_types,
    response_types,
    scopes,
    access_token_validity,
    refresh_token_validity,
    auth_code_validity,
    status,
    created_at,
    updated_at
) VALUES (
    'Go ABM IDP Client',
    'go-abm-idp-client',
    'ABM-GoIDP-2024-SecureSecret-Production',
    'confidential',
    'Go ABM IDP专用客户端，支持Apple Business Manager',
    '["https://gsa-ws.apple.com/grandslam/GsService2/acs", "https://openid.akapril.in/oauth/callback"]',
    '["authorization_code", "refresh_token", "client_credentials"]',
    '["code", "token", "id_token"]',
    '["openid", "profile", "email", "ssf.manage", "ssf.read", "offline_access"]',
    3600,      -- 1小时访问令牌
    2592000,   -- 30天刷新令牌
    600,       -- 10分钟授权码
    'active',
    NOW(),
    NOW()
);

-- 验证创建结果
SELECT 
    id,
    name,
    client_id,
    client_type,
    status,
    redirect_uris,
    grant_types,
    scopes,
    created_at
FROM oauth2_providers 
WHERE client_id IN ('apple-business-manager', 'go-abm-idp-client')
ORDER BY client_id;

-- 显示创建成功信息
SELECT 
    'Apple Business Manager客户端创建成功！' as message,
    'apple-business-manager' as primary_client_id,
    'go-abm-idp-client' as backup_client_id,
    'confidential' as client_type,
    'active' as status;

-- 使用说明
SELECT '=== Apple Business Manager 配置说明 ===' as info;
SELECT 'Primary Client ID: apple-business-manager' as credential_info
UNION ALL
SELECT 'Primary Client Secret: apple-client-secret' as credential_info
UNION ALL
SELECT 'Backup Client ID: go-abm-idp-client' as credential_info
UNION ALL
SELECT 'Backup Client Secret: ABM-GoIDP-2024-SecureSecret-Production' as credential_info
UNION ALL
SELECT 'Redirect URI: https://gsa-ws.apple.com/grandslam/GsService2/acs' as credential_info
UNION ALL
SELECT 'Grant Types: authorization_code, refresh_token' as credential_info
UNION ALL
SELECT 'Scopes: openid, profile, email, ssf.manage, ssf.read, offline_access' as credential_info;

-- 检查重定向URI配置
SELECT 
    '=== 重定向URI验证 ===' as check_info,
    client_id,
    JSON_EXTRACT(redirect_uris, '$[0]') as first_redirect_uri,
    JSON_VALID(redirect_uris) as json_valid
FROM oauth2_providers 
WHERE client_id IN ('apple-business-manager', 'go-abm-idp-client');
