# Build Linux version of go-abm-idp

Write-Host "Building Linux version of go-abm-idp..." -ForegroundColor Green

# Create bin directory if it doesn't exist
if (-not (Test-Path "bin")) {
    New-Item -ItemType Directory -Path "bin" -Force
    Write-Host "Created bin directory" -ForegroundColor Cyan
}

# Set environment variables for Linux cross-compilation
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"

Write-Host "Target OS: $env:GOOS" -ForegroundColor Cyan
Write-Host "Target Architecture: $env:GOARCH" -ForegroundColor Cyan
Write-Host "CGO Enabled: $env:CGO_ENABLED" -ForegroundColor Cyan

# Build the application
Write-Host "`nBuilding application..." -ForegroundColor Yellow

try {
    $buildCommand = "go build -ldflags='-w -s' -o bin/go-abm-idp-linux-amd64 cmd/server/main.go"
    Write-Host "Executing: $buildCommand" -ForegroundColor Gray
    
    Invoke-Expression $buildCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ Build successful!" -ForegroundColor Green
        
        # Check if the file was created
        if (Test-Path "bin/go-abm-idp-linux-amd64") {
            $fileInfo = Get-Item "bin/go-abm-idp-linux-amd64"
            Write-Host "📦 Output file: $($fileInfo.FullName)" -ForegroundColor Cyan
            Write-Host "📏 File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
            Write-Host "🕒 Created: $($fileInfo.CreationTime)" -ForegroundColor Cyan
        } else {
            Write-Host "❌ Build completed but output file not found!" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Build failed with exit code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Build error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Reset environment variables
    Remove-Item Env:GOOS -ErrorAction SilentlyContinue
    Remove-Item Env:GOARCH -ErrorAction SilentlyContinue
    Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue
}

Write-Host "`n🐧 Linux build process completed!" -ForegroundColor Green
