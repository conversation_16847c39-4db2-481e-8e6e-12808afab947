package service

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"log"
	"time"

	"go-abm-idp/internal/model"
)

// SSFEventService SSF事件服务
type SSFEventService struct {
	ssfService *SSFServiceImpl
}

// NewSSFEventService 创建SSF事件服务实例
func NewSSFEventService(ssfService SSFService) *SSFEventService {
	// 将接口转换为具体实现
	concreteSvc, ok := ssfService.(*SSFServiceImpl)
	if !ok {
		panic("SSFEventService requires concrete ssfService implementation")
	}
	return &SSFEventService{
		ssfService: concreteSvc,
	}
}

// SSFEventContext SSF事件上下文 (重命名避免冲突)
type SSFEventContext struct {
	UserID    string                 `json:"user_id"`
	UserEmail string                 `json:"user_email"`
	SessionID string                 `json:"session_id,omitempty"`
	ClientID  string                 `json:"client_id,omitempty"`
	IPAddress string                 `json:"ip_address,omitempty"`
	UserAgent string                 `json:"user_agent,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	ExtraData map[string]interface{} `json:"extra_data,omitempty"`
}

// UserLogoutEvent 用户登出事件
type UserLogoutEvent struct {
	SSFEventContext
	Reason string `json:"reason,omitempty"` // "user", "admin", "timeout", "policy"
}

// PasswordChangeEvent 密码变更事件
type PasswordChangeEvent struct {
	SSFEventContext
	ChangeType string `json:"change_type"` // "update", "reset", "revoke"
}

// AuthenticatorChangeEvent 认证器变更事件
type AuthenticatorChangeEvent struct {
	SSFEventContext
	DeviceType string `json:"device_type"` // "totp", "webauthn", "sms", "email"
	Action     string `json:"action"`      // "add", "remove", "update"
	DeviceID   string `json:"device_id,omitempty"`
}

// SessionRevokeEvent 会话撤销事件
type SessionRevokeEvent struct {
	SSFEventContext
	RevokedBy string `json:"revoked_by"` // "user", "admin", "system"
}

// HandleUserLogout 处理用户登出事件
func (s *SSFEventService) HandleUserLogout(ctx context.Context, event UserLogoutEvent) error {
	log.Printf("SSF: Handling user logout event for user %s", event.UserEmail)

	// 计算会话哈希
	sessionHash := sha256.Sum256([]byte(event.SessionID))
	sessionHashHex := hex.EncodeToString(sessionHash[:])

	// 构建事件数据
	eventData := map[string]interface{}{
		"initiating_entity": "user",
	}

	// 添加撤销原因
	if event.Reason != "" {
		eventData["reason"] = event.Reason
	}

	// 构建主题ID
	subID := map[string]interface{}{
		"format": "complex",
		"session": map[string]interface{}{
			"format": "opaque",
			"id":     sessionHashHex,
		},
		"user": map[string]interface{}{
			"format": "email",
			"email":  event.UserEmail,
		},
	}

	// 发送SSF事件
	return s.ssfService.sendEventToAllStreams(model.EventTypeSessionRevoked, eventData, subID)
}

// HandlePasswordChange 处理密码变更事件
func (s *SSFEventService) HandlePasswordChange(ctx context.Context, event PasswordChangeEvent) error {
	log.Printf("SSF: Handling password change event for user %s", event.UserEmail)

	// 构建事件数据
	eventData := map[string]interface{}{
		"credential_type": "password",
		"change_type":     event.ChangeType,
	}

	// 添加时间戳
	eventData["timestamp"] = event.Timestamp.Unix()

	// 构建主题ID
	subID := map[string]interface{}{
		"format": "complex",
		"user": map[string]interface{}{
			"format": "email",
			"email":  event.UserEmail,
		},
	}

	// 发送SSF事件
	return s.ssfService.sendEventToAllStreams(model.EventTypeCredentialChange, eventData, subID)
}

// HandleAuthenticatorChange 处理认证器变更事件
func (s *SSFEventService) HandleAuthenticatorChange(ctx context.Context, event AuthenticatorChangeEvent) error {
	log.Printf("SSF: Handling authenticator change event for user %s", event.UserEmail)

	// 构建事件数据
	eventData := map[string]interface{}{
		"credential_type": "authenticator",
		"change_type":     event.Action,
		"device_type":     event.DeviceType,
	}

	// 添加设备ID（如果有）
	if event.DeviceID != "" {
		eventData["device_id"] = event.DeviceID
	}

	// 添加时间戳
	eventData["timestamp"] = event.Timestamp.Unix()

	// 构建主题ID
	subID := map[string]interface{}{
		"format": "complex",
		"user": map[string]interface{}{
			"format": "email",
			"email":  event.UserEmail,
		},
	}

	// 发送SSF事件
	return s.ssfService.sendEventToAllStreams(model.EventTypeCredentialChange, eventData, subID)
}

// HandleSessionRevoke 处理会话撤销事件
func (s *SSFEventService) HandleSessionRevoke(ctx context.Context, event SessionRevokeEvent) error {
	log.Printf("SSF: Handling session revoke event for user %s", event.UserEmail)

	// 计算会话哈希
	sessionHash := sha256.Sum256([]byte(event.SessionID))
	sessionHashHex := hex.EncodeToString(sessionHash[:])

	// 构建事件数据
	eventData := map[string]interface{}{
		"initiating_entity": event.RevokedBy,
	}

	// 构建主题ID
	subID := map[string]interface{}{
		"format": "complex",
		"session": map[string]interface{}{
			"format": "opaque",
			"id":     sessionHashHex,
		},
		"user": map[string]interface{}{
			"format": "email",
			"email":  event.UserEmail,
		},
	}

	// 发送SSF事件
	return s.ssfService.sendEventToAllStreams(model.EventTypeSessionRevoked, eventData, subID)
}

// SSFEventListener SSF事件监听器接
type SSFEventListener interface {
	OnUserLogout(event UserLogoutEvent) error
	OnPasswordChange(event PasswordChangeEvent) error
	OnAuthenticatorChange(event AuthenticatorChangeEvent) error
	OnSessionRevoke(event SessionRevokeEvent) error
}

// DefaultSSFEventListener 默认SSF事件监听
type DefaultSSFEventListener struct {
	eventService *SSFEventService
}

// NewDefaultSSFEventListener 创建默认SSF事件监听
func NewDefaultSSFEventListener(eventService *SSFEventService) *DefaultSSFEventListener {
	return &DefaultSSFEventListener{
		eventService: eventService,
	}
}

// OnUserLogout 用户登出事件处理
func (l *DefaultSSFEventListener) OnUserLogout(event UserLogoutEvent) error {
	return l.eventService.HandleUserLogout(context.Background(), event)
}

// OnPasswordChange 密码变更事件处理
func (l *DefaultSSFEventListener) OnPasswordChange(event PasswordChangeEvent) error {
	return l.eventService.HandlePasswordChange(context.Background(), event)
}

// OnAuthenticatorChange 认证器变更事件处
func (l *DefaultSSFEventListener) OnAuthenticatorChange(event AuthenticatorChangeEvent) error {
	return l.eventService.HandleAuthenticatorChange(context.Background(), event)
}

// OnSessionRevoke 会话撤销事件处理
func (l *DefaultSSFEventListener) OnSessionRevoke(event SessionRevokeEvent) error {
	return l.eventService.HandleSessionRevoke(context.Background(), event)
}

// SSFEventBus SSF事件总线
type SSFEventBus struct {
	listeners []SSFEventListener
}

// NewSSFEventBus 创建SSF事件总线
func NewSSFEventBus() *SSFEventBus {
	return &SSFEventBus{
		listeners: make([]SSFEventListener, 0),
	}
}

// AddListener 添加事件监听
func (bus *SSFEventBus) AddListener(listener SSFEventListener) {
	bus.listeners = append(bus.listeners, listener)
}

// PublishUserLogout 发布用户登出事件
func (bus *SSFEventBus) PublishUserLogout(event UserLogoutEvent) {
	for _, listener := range bus.listeners {
		go func(l SSFEventListener) {
			if err := l.OnUserLogout(event); err != nil {
				log.Printf("SSF: Error handling user logout event: %v", err)
			}
		}(listener)
	}
}

// PublishPasswordChange 发布密码变更事件
func (bus *SSFEventBus) PublishPasswordChange(event PasswordChangeEvent) {
	for _, listener := range bus.listeners {
		go func(l SSFEventListener) {
			if err := l.OnPasswordChange(event); err != nil {
				log.Printf("SSF: Error handling password change event: %v", err)
			}
		}(listener)
	}
}

// PublishAuthenticatorChange 发布认证器变更事
func (bus *SSFEventBus) PublishAuthenticatorChange(event AuthenticatorChangeEvent) {
	for _, listener := range bus.listeners {
		go func(l SSFEventListener) {
			if err := l.OnAuthenticatorChange(event); err != nil {
				log.Printf("SSF: Error handling authenticator change event: %v", err)
			}
		}(listener)
	}
}

// PublishSessionRevoke 发布会话撤销事件
func (bus *SSFEventBus) PublishSessionRevoke(event SessionRevokeEvent) {
	for _, listener := range bus.listeners {
		go func(l SSFEventListener) {
			if err := l.OnSessionRevoke(event); err != nil {
				log.Printf("SSF: Error handling session revoke event: %v", err)
			}
		}(listener)
	}
}

// 全局事件总线实例
var GlobalSSFEventBus *SSFEventBus

// InitSSFEventBus 初始化SSF事件总线
func InitSSFEventBus(ssfService SSFService) {
	GlobalSSFEventBus = NewSSFEventBus()

	// 创建并添加默认事件监听器
	eventService := NewSSFEventService(ssfService)
	defaultListener := NewDefaultSSFEventListener(eventService)
	GlobalSSFEventBus.AddListener(defaultListener)

	log.Println("SSF: Event bus initialized")
}

// 便捷函数,用于在应用程序中触发SSF事件

// TriggerUserLogoutEvent 触发用户登出事件
func TriggerUserLogoutEvent(userEmail, sessionID, reason string) {
	if GlobalSSFEventBus == nil {
		return
	}

	event := UserLogoutEvent{
		SSFEventContext: SSFEventContext{
			UserEmail: userEmail,
			SessionID: sessionID,
			Timestamp: time.Now(),
		},
		Reason: reason,
	}

	GlobalSSFEventBus.PublishUserLogout(event)
}

// TriggerPasswordChangeEvent 触发密码变更事件
func TriggerPasswordChangeEvent(userEmail, changeType string) {
	if GlobalSSFEventBus == nil {
		return
	}

	event := PasswordChangeEvent{
		SSFEventContext: SSFEventContext{
			UserEmail: userEmail,
			Timestamp: time.Now(),
		},
		ChangeType: changeType,
	}

	GlobalSSFEventBus.PublishPasswordChange(event)
}

// TriggerAuthenticatorChangeEvent 触发认证器变更事
func TriggerAuthenticatorChangeEvent(userEmail, deviceType, action, deviceID string) {
	if GlobalSSFEventBus == nil {
		return
	}

	event := AuthenticatorChangeEvent{
		SSFEventContext: SSFEventContext{
			UserEmail: userEmail,
			Timestamp: time.Now(),
		},
		DeviceType: deviceType,
		Action:     action,
		DeviceID:   deviceID,
	}

	GlobalSSFEventBus.PublishAuthenticatorChange(event)
}

// TriggerSessionRevokeEvent 触发会话撤销事件
func TriggerSessionRevokeEvent(userEmail, sessionID, revokedBy string) {
	if GlobalSSFEventBus == nil {
		return
	}

	event := SessionRevokeEvent{
		SSFEventContext: SSFEventContext{
			UserEmail: userEmail,
			SessionID: sessionID,
			Timestamp: time.Now(),
		},
		RevokedBy: revokedBy,
	}

	GlobalSSFEventBus.PublishSessionRevoke(event)
}
