# Generated by Django 5.0.2 on 2024-02-25 20:44

import authentik.lib.utils.time
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authentik_core", "0033_alter_user_options"),
        ("authentik_flows", "0027_auto_20231028_1424"),
    ]

    operations = [
        migrations.CreateModel(
            name="SourceStage",
            fields=[
                (
                    "stage_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="authentik_flows.stage",
                    ),
                ),
                (
                    "resume_timeout",
                    models.TextField(
                        default="minutes=10",
                        help_text="Amount of time a user can take to return from the source to continue the flow (Format: hours=-1;minutes=-2;seconds=-3)",
                        validators=[authentik.lib.utils.time.timedelta_string_validator],
                    ),
                ),
                (
                    "source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="authentik_core.source"
                    ),
                ),
            ],
            options={
                "verbose_name": "Source Stage",
                "verbose_name_plural": "Source Stages",
            },
            bases=("authentik_flows.stage",),
        ),
    ]
