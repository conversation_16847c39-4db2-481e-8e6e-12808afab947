#!/bin/bash

# OAuth2/OIDC 标准符合性验证脚本
# 用于验证Go ABM IDP是否符合OAuth2和OpenID Connect标准

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="${BASE_URL:-http://localhost:8080}"
CLIENT_ID="${CLIENT_ID:-test_client}"
CLIENT_SECRET="${CLIENT_SECRET:-test_secret}"
REDIRECT_URI="${REDIRECT_URI:-http://localhost:3000/callback}"

echo -e "${BLUE}=== OAuth2/OIDC 标准符合性验证 ===${NC}"
echo "Base URL: $BASE_URL"
echo ""

# 检查函数
check_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_status=${3:-200}
    local method=${4:-GET}
    
    echo -n "检查 $description... "
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL$endpoint" -H "Content-Type: application/x-www-form-urlencoded" -d "" 2>/dev/null)
    else
        response=$(curl -s -w "%{http_code}" "$BASE_URL$endpoint" 2>/dev/null)
    fi
    
    status_code="${response: -3}"
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓${NC}"
        return 0
    else
        echo -e "${RED}✗ (状态码: $status_code)${NC}"
        return 1
    fi
}

check_json_response() {
    local endpoint=$1
    local description=$2
    local required_fields=$3
    
    echo -n "检查 $description... "
    
    response=$(curl -s "$BASE_URL$endpoint" 2>/dev/null)
    
    # 检查是否为有效JSON
    if ! echo "$response" | jq . >/dev/null 2>&1; then
        echo -e "${RED}✗ (无效JSON)${NC}"
        return 1
    fi
    
    # 检查必需字段
    for field in $required_fields; do
        if ! echo "$response" | jq -e ".$field" >/dev/null 2>&1; then
            echo -e "${RED}✗ (缺少字段: $field)${NC}"
            return 1
        fi
    done
    
    echo -e "${GREEN}✓${NC}"
    return 0
}

check_error_response() {
    local endpoint=$1
    local description=$2
    local method=${3:-POST}
    
    echo -n "检查 $description... "
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -X POST "$BASE_URL$endpoint" -H "Content-Type: application/x-www-form-urlencoded" -d "invalid=data" 2>/dev/null)
    else
        response=$(curl -s "$BASE_URL$endpoint?invalid=data" 2>/dev/null)
    fi
    
    # 检查是否包含标准错误字段
    if echo "$response" | jq -e '.error' >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
        return 0
    else
        echo -e "${RED}✗ (错误响应格式不标准)${NC}"
        return 1
    fi
}

check_content_type() {
    local endpoint=$1
    local description=$2
    local method=${3:-GET}
    
    echo -n "检查 $description Content-Type... "
    
    if [ "$method" = "POST" ]; then
        content_type=$(curl -s -I -X POST "$BASE_URL$endpoint" -H "Content-Type: application/x-www-form-urlencoded" -d "" 2>/dev/null | grep -i "content-type" | cut -d: -f2 | tr -d ' \r\n')
    else
        content_type=$(curl -s -I "$BASE_URL$endpoint" 2>/dev/null | grep -i "content-type" | cut -d: -f2 | tr -d ' \r\n')
    fi
    
    if [[ "$content_type" == *"application/json"* ]]; then
        echo -e "${GREEN}✓${NC}"
        return 0
    else
        echo -e "${RED}✗ (Content-Type: $content_type)${NC}"
        return 1
    fi
}

check_security_headers() {
    local endpoint=$1
    local description=$2
    
    echo -n "检查 $description 安全头... "
    
    headers=$(curl -s -I "$BASE_URL$endpoint" 2>/dev/null)
    
    security_score=0
    total_headers=5
    
    if echo "$headers" | grep -qi "cache-control.*no-store"; then
        ((security_score++))
    fi
    
    if echo "$headers" | grep -qi "pragma.*no-cache"; then
        ((security_score++))
    fi
    
    if echo "$headers" | grep -qi "x-content-type-options"; then
        ((security_score++))
    fi
    
    if echo "$headers" | grep -qi "x-frame-options"; then
        ((security_score++))
    fi
    
    if echo "$headers" | grep -qi "x-xss-protection"; then
        ((security_score++))
    fi
    
    if [ $security_score -ge 3 ]; then
        echo -e "${GREEN}✓ ($security_score/$total_headers)${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠ ($security_score/$total_headers)${NC}"
        return 1
    fi
}

# 1. OAuth2 Core端点检查
echo -e "${YELLOW}1. OAuth2 Core 端点检查 (RFC 6749)${NC}"

check_endpoint "/oauth2/authorize" "授权端点" 400
check_endpoint "/oauth2/token" "令牌端点" 400 POST
check_content_type "/oauth2/token" "令牌端点" POST

echo ""

# 2. OpenID Connect Discovery检查
echo -e "${YELLOW}2. OpenID Connect Discovery 检查${NC}"

check_json_response "/.well-known/openid-configuration" "OIDC配置端点" "issuer authorization_endpoint token_endpoint userinfo_endpoint jwks_uri device_authorization_endpoint"
check_json_response "/oauth2/jwks" "JWKS端点" "keys"
check_content_type "/.well-known/openid-configuration" "OIDC配置端点"

# 检查高级特性支持
echo -n "检查JWT访问令牌支持... "
if curl -s "/.well-known/openid-configuration" | jq -e '.access_token_signing_alg_values_supported' >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ (不支持JWT访问令牌)${NC}"
fi

echo -n "检查令牌绑定支持... "
if curl -s "/.well-known/openid-configuration" | jq -e '.token_binding_supported' >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ (不支持令牌绑定)${NC}"
fi

echo -n "检查会话管理支持... "
if curl -s "/.well-known/openid-configuration" | jq -e '.check_session_iframe' >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ (不支持会话管理)${NC}"
fi

echo ""

# 3. 错误响应格式检查
echo -e "${YELLOW}3. 错误响应格式检查${NC}"

check_error_response "/oauth2/token" "令牌端点错误响应" POST
check_error_response "/oauth2/authorize" "授权端点错误响应" GET

echo ""

# 4. 安全头检查
echo -e "${YELLOW}4. 安全头检查${NC}"

check_security_headers "/oauth2/token" "令牌端点"
check_security_headers "/.well-known/openid-configuration" "OIDC配置端点"

echo ""

# 5. PKCE支持检查
echo -e "${YELLOW}5. PKCE 支持检查 (RFC 7636)${NC}"

echo -n "检查PKCE参数支持... "
response=$(curl -s -X POST "$BASE_URL/oauth2/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=authorization_code&code=test&code_verifier=test" 2>/dev/null)

if echo "$response" | jq -e '.error' >/dev/null 2>&1; then
    error_code=$(echo "$response" | jq -r '.error')
    if [ "$error_code" != "unsupported_grant_type" ]; then
        echo -e "${GREEN}✓ (PKCE参数被识别)${NC}"
    else
        echo -e "${YELLOW}⚠ (PKCE支持未确认)${NC}"
    fi
else
    echo -e "${RED}✗ (无法验证PKCE支持)${NC}"
fi

echo ""

# 6. 令牌撤销和内省端点检查
echo -e "${YELLOW}6. 扩展端点检查${NC}"

check_endpoint "/oauth2/revoke" "令牌撤销端点 (RFC 7009)" 400 POST
check_endpoint "/oauth2/introspect" "令牌内省端点 (RFC 7662)" 400 POST
check_endpoint "/oauth2/userinfo" "用户信息端点" 401
check_endpoint "/oauth2/device_authorization" "设备授权端点 (RFC 8628)" 400 POST
check_endpoint "/oauth2/device" "设备验证端点" 200

echo ""

# 7. 端点一致性检查
echo -e "${YELLOW}7. 端点一致性检查${NC}"

echo -n "检查端点路径一致性... "
# 检查是否存在重复的端点路径
duplicate_paths=0

# 检查/oauth2/和/application/o/路径是否都存在
oauth2_auth=$(curl -s -w "%{http_code}" "$BASE_URL/oauth2/authorize" 2>/dev/null | tail -c 3)
app_auth=$(curl -s -w "%{http_code}" "$BASE_URL/application/o/authorize/" 2>/dev/null | tail -c 3)

if [ "$oauth2_auth" != "404" ] && [ "$app_auth" != "404" ]; then
    echo -e "${YELLOW}⚠ (存在多套端点路径)${NC}"
    duplicate_paths=1
else
    echo -e "${GREEN}✓${NC}"
fi

echo ""

# 8. 高级特性检查
echo -e "${YELLOW}8. 高级特性检查${NC}"

# JWT访问令牌检查
echo -n "检查JWT访问令牌格式... "
# 这里需要实际的JWT令牌来测试，暂时跳过
echo -e "${YELLOW}⚠ (需要实际令牌测试)${NC}"

# 令牌绑定检查
echo -n "检查令牌绑定头支持... "
response=$(curl -s -I -H "Sec-Token-Binding: test" "$BASE_URL/oauth2/userinfo" 2>/dev/null)
if echo "$response" | grep -qi "HTTP"; then
    echo -e "${GREEN}✓ (接受令牌绑定头)${NC}"
else
    echo -e "${YELLOW}⚠ (令牌绑定头处理未确认)${NC}"
fi

# 会话管理检查
echo -n "检查会话管理iframe... "
if curl -s "$BASE_URL/oidc/check_session" 2>/dev/null | grep -qi "html\|iframe"; then
    echo -e "${GREEN}✓${NC}"
else
    echo -e "${YELLOW}⚠ (会话管理iframe未实现)${NC}"
fi

# CORS配置检查
echo -n "检查CORS配置... "
cors_headers=$(curl -s -I -H "Origin: http://localhost:3000" "$BASE_URL/.well-known/openid-configuration" 2>/dev/null)
if echo "$cors_headers" | grep -qi "access-control-allow-origin"; then
    echo -e "${GREEN}✓ (CORS已配置)${NC}"
else
    echo -e "${YELLOW}⚠ (CORS未配置)${NC}"
fi

echo ""

# 9. 综合评分
echo -e "${YELLOW}9. 综合评分${NC}"

total_checks=20
passed_checks=0

# 重新运行所有检查并计分
{
    check_endpoint "/oauth2/authorize" "授权端点" 400 >/dev/null 2>&1 && ((passed_checks++))
    check_endpoint "/oauth2/token" "令牌端点" 400 POST >/dev/null 2>&1 && ((passed_checks++))
    check_json_response "/.well-known/openid-configuration" "OIDC配置" "issuer authorization_endpoint token_endpoint" >/dev/null 2>&1 && ((passed_checks++))
    check_json_response "/oauth2/jwks" "JWKS端点" "keys" >/dev/null 2>&1 && ((passed_checks++))
    check_error_response "/oauth2/token" "令牌端点错误响应" POST >/dev/null 2>&1 && ((passed_checks++))
    check_content_type "/oauth2/token" "令牌端点" POST >/dev/null 2>&1 && ((passed_checks++))
    check_security_headers "/oauth2/token" "令牌端点" >/dev/null 2>&1 && ((passed_checks++))
    check_endpoint "/oauth2/revoke" "令牌撤销端点" 400 POST >/dev/null 2>&1 && ((passed_checks++))
    check_endpoint "/oauth2/introspect" "令牌内省端点" 400 POST >/dev/null 2>&1 && ((passed_checks++))
    check_endpoint "/oauth2/userinfo" "用户信息端点" 401 >/dev/null 2>&1 && ((passed_checks++))
}

compliance_score=$((passed_checks * 100 / total_checks))

echo "通过检查: $passed_checks/$total_checks"
echo "符合性评分: $compliance_score%"

if [ $compliance_score -ge 90 ]; then
    echo -e "${GREEN}✅ 优秀 - 高度符合OAuth2/OIDC标准${NC}"
elif [ $compliance_score -ge 75 ]; then
    echo -e "${YELLOW}⚠️  良好 - 基本符合标准，有改进空间${NC}"
else
    echo -e "${RED}❌ 需要改进 - 存在重要的标准符合性问题${NC}"
fi

echo ""

# 9. 改进建议
echo -e "${YELLOW}9. 改进建议${NC}"

if [ $duplicate_paths -eq 1 ]; then
    echo "- 统一端点路径，避免多套路径系统"
fi

if [ $compliance_score -lt 90 ]; then
    echo "- 完善错误响应格式，确保包含所有标准字段"
    echo "- 加强安全头配置"
    echo "- 验证Content-Type头设置"
fi

echo "- 考虑实施OAuth2安全最佳实践"
echo "- 定期进行标准符合性测试"

echo ""
echo -e "${BLUE}=== 验证完成 ===${NC}"
echo ""
echo "详细的标准符合性分析请参考: OAUTH2_STANDARDS_COMPLIANCE_ANALYSIS.md"
