# Apple Business Manager OIDC Endpoint Path Consistency Verification

param(
    [string]$BaseURL = "https://openid.akapril.in"
)

Write-Host "OIDC Endpoint Path Consistency Verification" -ForegroundColor Green
Write-Host "Target URL: $BaseURL" -ForegroundColor Cyan

$Results = @{ Passed = 0; Failed = 0; Total = 0 }

function Test-EndpointPath {
    param(
        [string]$EndpointName,
        [string]$DeclaredPath,
        [string]$Method = "GET"
    )
    
    $Results.Total++
    Write-Host "`nTesting: $EndpointName" -ForegroundColor Yellow
    Write-Host "  Declared Path: $DeclaredPath" -ForegroundColor Gray
    Write-Host "  HTTP Method: $Method" -ForegroundColor Gray
    
    try {
        $fullURL = "$BaseURL$DeclaredPath"
        
        if ($Method -eq "GET") {
            $response = Invoke-WebRequest -Uri $fullURL -Method GET -UseBasicParsing -ErrorAction Stop
            Write-Host "  PASS: Endpoint accessible (Status: $($response.StatusCode))" -ForegroundColor Green
            $Results.Passed++
        } else {
            # For POST endpoints, check if they exist (may return 405 or 400)
            try {
                $response = Invoke-WebRequest -Uri $fullURL -Method $Method -UseBasicParsing -ErrorAction Stop
                Write-Host "  PASS: Endpoint accessible (Status: $($response.StatusCode))" -ForegroundColor Green
                $Results.Passed++
            } catch {
                if ($_.Exception.Response.StatusCode -eq 405) {
                    Write-Host "  PASS: Endpoint exists (405 Method Not Allowed - expected)" -ForegroundColor Green
                    $Results.Passed++
                } elseif ($_.Exception.Response.StatusCode -eq 400) {
                    Write-Host "  PASS: Endpoint exists (400 Bad Request - expected)" -ForegroundColor Green
                    $Results.Passed++
                } else {
                    throw
                }
            }
        }
        
    } catch {
        if ($_.Exception.Response.StatusCode -eq 404) {
            Write-Host "  FAIL: Endpoint not found (404)" -ForegroundColor Red
        } else {
            Write-Host "  WARNING: Endpoint access error: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        $Results.Failed++
    }
}

# Get OIDC configuration
Write-Host "`nFetching OIDC configuration..." -ForegroundColor Blue
try {
    $oidcConfig = Invoke-RestMethod -Uri "$BaseURL/.well-known/openid_configuration" -UseBasicParsing
    Write-Host "OIDC configuration retrieved successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to get OIDC configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Extract endpoint paths
$endpoints = @{
    "Authorization Endpoint" = @{
        "path" = $oidcConfig.authorization_endpoint -replace "^https?://[^/]+", ""
        "method" = "GET"
    }
    "Token Endpoint" = @{
        "path" = $oidcConfig.token_endpoint -replace "^https?://[^/]+", ""
        "method" = "POST"
    }
    "UserInfo Endpoint" = @{
        "path" = $oidcConfig.userinfo_endpoint -replace "^https?://[^/]+", ""
        "method" = "GET"
    }
    "JWKS Endpoint" = @{
        "path" = $oidcConfig.jwks_uri -replace "^https?://[^/]+", ""
        "method" = "GET"
    }
}

# Test each endpoint
Write-Host "`nStarting endpoint path verification..." -ForegroundColor Blue

foreach ($endpointName in $endpoints.Keys) {
    $endpoint = $endpoints[$endpointName]
    Test-EndpointPath -EndpointName $endpointName -DeclaredPath $endpoint.path -Method $endpoint.method
}

# Test root JWKS redirect
Write-Host "`nTesting root JWKS redirect..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "$BaseURL/jwks" -UseBasicParsing -MaximumRedirection 0 -ErrorAction Stop
    Write-Host "  FAIL: Root JWKS not redirecting (Status: $($response.StatusCode))" -ForegroundColor Red
    $Results.Failed++
} catch {
    if ($_.Exception.Response.StatusCode -eq 301 -or $_.Exception.Response.StatusCode -eq 302) {
        $location = $_.Exception.Response.Headers.Location
        Write-Host "  PASS: Root JWKS correctly redirects to: $location" -ForegroundColor Green
        $Results.Passed++
    } else {
        Write-Host "  FAIL: Root JWKS redirect error: $($_.Exception.Message)" -ForegroundColor Red
        $Results.Failed++
    }
}
$Results.Total++

# Output results
Write-Host "`nEndpoint Path Consistency Results" -ForegroundColor Magenta
Write-Host "Total Tests: $($Results.Total)" -ForegroundColor Cyan
Write-Host "Passed: $($Results.Passed)" -ForegroundColor Green
Write-Host "Failed: $($Results.Failed)" -ForegroundColor Red

$consistencyScore = if ($Results.Total -gt 0) { 
    [math]::Round(($Results.Passed / $Results.Total) * 100, 2) 
} else { 0 }

Write-Host "Consistency Score: $consistencyScore%" -ForegroundColor $(
    if ($consistencyScore -ge 95) { "Green" } 
    elseif ($consistencyScore -ge 80) { "Yellow" } 
    else { "Red" }
)

# Show OIDC configuration endpoints
Write-Host "`nOIDC Configuration Endpoints:" -ForegroundColor Magenta
Write-Host "Issuer: $($oidcConfig.issuer)" -ForegroundColor Gray
Write-Host "Authorization: $($oidcConfig.authorization_endpoint)" -ForegroundColor Gray
Write-Host "Token: $($oidcConfig.token_endpoint)" -ForegroundColor Gray
Write-Host "UserInfo: $($oidcConfig.userinfo_endpoint)" -ForegroundColor Gray
Write-Host "JWKS: $($oidcConfig.jwks_uri)" -ForegroundColor Gray

# Final recommendations
Write-Host "`nEndpoint Path Consistency Status" -ForegroundColor Magenta

if ($Results.Failed -eq 0) {
    Write-Host "SUCCESS: All endpoint paths are consistent with OIDC configuration!" -ForegroundColor Green
} else {
    Write-Host "ATTENTION: Found path inconsistency issues, please check route configuration." -ForegroundColor Red
}

Write-Host "`nApple Business Manager Integration Recommendations:" -ForegroundColor Cyan
Write-Host "1. Ensure all endpoint paths match OIDC configuration exactly" -ForegroundColor Gray
Write-Host "2. Verify HTTP method support is correct" -ForegroundColor Gray
Write-Host "3. Check redirect configurations work properly" -ForegroundColor Gray
Write-Host "4. Confirm SSL certificate configuration is correct" -ForegroundColor Gray

Write-Host "`nEndpoint Path Consistency Verification Complete!" -ForegroundColor Green
