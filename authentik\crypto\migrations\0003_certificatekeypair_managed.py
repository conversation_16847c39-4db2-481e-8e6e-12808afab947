# Generated by Django 3.2.8 on 2021-10-09 17:05

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentik_crypto", "0002_create_self_signed_kp"),
    ]

    operations = [
        migrations.AddField(
            model_name="certificatekeypair",
            name="managed",
            field=models.TextField(
                default=None,
                help_text=(
                    "Objects which are managed by authentik. These objects are created and updated"
                    " automatically. This is flag only indicates that an object can be overwritten"
                    " by migrations. You can still modify the objects via the API, but expect"
                    " changes to be overwritten in a later update."
                ),
                null=True,
                unique=True,
                verbose_name="Managed by authentik",
            ),
        ),
    ]
