@echo off
echo Building Linux version of go-abm-idp...

REM Create bin directory if it doesn't exist
if not exist "bin" mkdir bin

REM Set environment variables for Linux cross-compilation
set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0

echo Target OS: %GOOS%
echo Target Architecture: %GOARCH%
echo CGO Enabled: %CGO_ENABLED%

echo.
echo Building application...

REM Build the application with optimizations
go build -ldflags="-w -s" -o bin/go-abm-idp-linux-amd64 cmd/server/main.go

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo 📦 Output file: bin/go-abm-idp-linux-amd64
    
    REM Show file size
    for %%A in (bin\go-abm-idp-linux-amd64) do (
        echo 📏 File size: %%~zA bytes
    )
) else (
    echo.
    echo ❌ Build failed with error code: %ERRORLEVEL%
)

REM Reset environment variables
set GOOS=
set GOARCH=
set CGO_ENABLED=

echo.
echo 🐧 Linux build process completed!
pause
