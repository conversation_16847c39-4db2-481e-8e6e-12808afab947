package main

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// OIDCServiceMigrator OIDC服务迁移工具
type OIDCServiceMigrator struct {
	projectRoot string
	backupDir   string
	dryRun      bool
}

// NewOIDCServiceMigrator 创建迁移工具
func NewOIDCServiceMigrator(projectRoot string, dryRun bool) *OIDCServiceMigrator {
	return &OIDCServiceMigrator{
		projectRoot: projectRoot,
		backupDir:   filepath.Join(projectRoot, "backup_oidc_migration"),
		dryRun:      dryRun,
	}
}

// Migrate 执行OIDC服务统一迁移
func (m *OIDCServiceMigrator) Migrate() error {
	fmt.Println("🚀 开始OIDC服务统一迁移...")
	
	// 1. 创建备份
	if err := m.createBackup(); err != nil {
		return fmt.Errorf("创建备份失败: %w", err)
	}
	
	// 2. 更新Wire配置
	if err := m.updateWireConfig(); err != nil {
		return fmt.Errorf("更新Wire配置失败: %w", err)
	}
	
	// 3. 更新Handler依赖
	if err := m.updateHandlerDependencies(); err != nil {
		return fmt.Errorf("更新Handler依赖失败: %w", err)
	}
	
	// 4. 验证迁移
	if err := m.validateMigration(); err != nil {
		return fmt.Errorf("验证迁移失败: %w", err)
	}
	
	fmt.Println("✅ OIDC服务统一迁移完成!")
	fmt.Println("📁 备份文件位置:", m.backupDir)
	fmt.Println("🔧 请运行以下命令验证:")
	fmt.Println("   go build ./cmd/server")
	fmt.Println("   go test ./internal/service/...")
	
	return nil
}

// createBackup 创建备份
func (m *OIDCServiceMigrator) createBackup() error {
	fmt.Println("📦 创建备份...")
	
	if !m.dryRun {
		if err := os.MkdirAll(m.backupDir, 0755); err != nil {
			return err
		}
	}
	
	// 备份需要修改的文件
	filesToBackup := []string{
		"cmd/server/wire/wire.go",
		"cmd/server/wire/wire_gen.go",
		"internal/handler/oauth2_standard.go",
		"internal/handler/oauth2_admin.go",
		"internal/service/oidc_service.go",
		"internal/service/unified_oidc_service.go",
		"internal/service/oidc_enhanced_service.go",
	}
	
	for _, file := range filesToBackup {
		srcPath := filepath.Join(m.projectRoot, file)
		if _, err := os.Stat(srcPath); os.IsNotExist(err) {
			continue // 文件不存在，跳过
		}
		
		dstPath := filepath.Join(m.backupDir, file)
		if err := m.copyFile(srcPath, dstPath); err != nil {
			return fmt.Errorf("备份文件 %s 失败: %w", file, err)
		}
		fmt.Printf("  ✓ 已备份: %s\n", file)
	}
	
	return nil
}

// updateWireConfig 更新Wire配置
func (m *OIDCServiceMigrator) updateWireConfig() error {
	fmt.Println("🔧 更新Wire配置...")
	
	wireFile := filepath.Join(m.projectRoot, "cmd/server/wire/wire.go")
	content, err := ioutil.ReadFile(wireFile)
	if err != nil {
		return err
	}
	
	// 替换OIDC服务集合
	oldPattern := `service\.NewOIDCService,`
	newReplacement := `service.OIDCUnifiedServiceSet,`
	
	newContent := strings.ReplaceAll(string(content), oldPattern, newReplacement)
	
	// 添加导入
	if !strings.Contains(newContent, `"go-abm-idp/internal/service"`) {
		// 在import块中添加service包
		importPattern := regexp.MustCompile(`import \(\s*\n`)
		newContent = importPattern.ReplaceAllString(newContent, `import (
	"go-abm-idp/internal/service"
`)
	}
	
	if !m.dryRun {
		if err := ioutil.WriteFile(wireFile, []byte(newContent), 0644); err != nil {
			return err
		}
	}
	
	fmt.Println("  ✓ 已更新Wire配置")
	return nil
}

// updateHandlerDependencies 更新Handler依赖
func (m *OIDCServiceMigrator) updateHandlerDependencies() error {
	fmt.Println("🔧 更新Handler依赖...")
	
	handlerFiles := []string{
		"internal/handler/oauth2_standard.go",
		"internal/handler/oauth2_admin.go",
	}
	
	for _, file := range handlerFiles {
		filePath := filepath.Join(m.projectRoot, file)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			continue
		}
		
		content, err := ioutil.ReadFile(filePath)
		if err != nil {
			return err
		}
		
		// 替换服务类型
		newContent := strings.ReplaceAll(string(content), 
			"oidcService service.OIDCService", 
			"oidcService service.OIDCUnifiedService")
		
		if !m.dryRun {
			if err := ioutil.WriteFile(filePath, []byte(newContent), 0644); err != nil {
				return err
			}
		}
		
		fmt.Printf("  ✓ 已更新: %s\n", file)
	}
	
	return nil
}

// validateMigration 验证迁移
func (m *OIDCServiceMigrator) validateMigration() error {
	fmt.Println("🔍 验证迁移...")
	
	// 检查关键文件是否存在
	requiredFiles := []string{
		"internal/service/oidc_unified.go",
		"internal/service/oidc_wire_integration.go",
	}
	
	for _, file := range requiredFiles {
		filePath := filepath.Join(m.projectRoot, file)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			return fmt.Errorf("缺少必需文件: %s", file)
		}
	}
	
	fmt.Println("  ✓ 所有必需文件存在")
	return nil
}

// copyFile 复制文件
func (m *OIDCServiceMigrator) copyFile(src, dst string) error {
	if m.dryRun {
		return nil
	}
	
	// 创建目标目录
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}
	
	// 复制文件
	input, err := ioutil.ReadFile(src)
	if err != nil {
		return err
	}
	
	return ioutil.WriteFile(dst, input, 0644)
}

// rollback 回滚迁移
func (m *OIDCServiceMigrator) Rollback() error {
	fmt.Println("🔄 开始回滚OIDC服务迁移...")
	
	if _, err := os.Stat(m.backupDir); os.IsNotExist(err) {
		return fmt.Errorf("备份目录不存在: %s", m.backupDir)
	}
	
	// 恢复备份文件
	err := filepath.Walk(m.backupDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if info.IsDir() {
			return nil
		}
		
		// 计算相对路径
		relPath, err := filepath.Rel(m.backupDir, path)
		if err != nil {
			return err
		}
		
		// 恢复文件
		dstPath := filepath.Join(m.projectRoot, relPath)
		return m.copyFile(path, dstPath)
	})
	
	if err != nil {
		return fmt.Errorf("回滚失败: %w", err)
	}
	
	fmt.Println("✅ 回滚完成!")
	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run migrate_oidc_services.go <command> [options]")
		fmt.Println("命令:")
		fmt.Println("  migrate    执行迁移")
		fmt.Println("  rollback   回滚迁移")
		fmt.Println("选项:")
		fmt.Println("  --dry-run  仅显示将要执行的操作，不实际修改文件")
		os.Exit(1)
	}
	
	command := os.Args[1]
	dryRun := len(os.Args) > 2 && os.Args[2] == "--dry-run"
	
	// 获取项目根目录
	projectRoot, err := os.Getwd()
	if err != nil {
		fmt.Printf("获取当前目录失败: %v\n", err)
		os.Exit(1)
	}
	
	// 确保在项目根目录
	if _, err := os.Stat(filepath.Join(projectRoot, "go.mod")); os.IsNotExist(err) {
		fmt.Println("请在项目根目录运行此脚本")
		os.Exit(1)
	}
	
	migrator := NewOIDCServiceMigrator(projectRoot, dryRun)
	
	switch command {
	case "migrate":
		if dryRun {
			fmt.Println("🔍 DRY RUN 模式 - 仅显示将要执行的操作")
		}
		
		fmt.Println("⚠️  此操作将修改项目文件，确保已提交当前更改到版本控制系统")
		fmt.Print("继续? (y/N): ")
		
		reader := bufio.NewReader(os.Stdin)
		response, _ := reader.ReadString('\n')
		response = strings.TrimSpace(strings.ToLower(response))
		
		if response != "y" && response != "yes" {
			fmt.Println("操作已取消")
			os.Exit(0)
		}
		
		if err := migrator.Migrate(); err != nil {
			fmt.Printf("迁移失败: %v\n", err)
			os.Exit(1)
		}
		
	case "rollback":
		if err := migrator.Rollback(); err != nil {
			fmt.Printf("回滚失败: %v\n", err)
			os.Exit(1)
		}
		
	default:
		fmt.Printf("未知命令: %s\n", command)
		os.Exit(1)
	}
}
