#!/bin/bash

# Linux Deployment Script for go-abm-idp
# Apple Business Manager Identity Provider

set -e

echo "🐧 go-abm-idp Linux Deployment Script"
echo "======================================"

# Configuration
APP_NAME="go-abm-idp"
APP_USER="abm-idp"
APP_DIR="/opt/go-abm-idp"
CONFIG_DIR="/etc/go-abm-idp"
LOG_DIR="/var/log/go-abm-idp"
SERVICE_NAME="go-abm-idp"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Create application user
create_user() {
    print_status "Creating application user: $APP_USER"
    if ! id "$APP_USER" &>/dev/null; then
        useradd --system --shell /bin/false --home-dir $APP_DIR --create-home $APP_USER
        print_success "User $APP_USER created"
    else
        print_warning "User $APP_USER already exists"
    fi
}

# Create directories
create_directories() {
    print_status "Creating application directories"
    
    mkdir -p $APP_DIR/{bin,config,storage,logs}
    mkdir -p $CONFIG_DIR
    mkdir -p $LOG_DIR
    
    # Set ownership
    chown -R $APP_USER:$APP_USER $APP_DIR
    chown -R $APP_USER:$APP_USER $LOG_DIR
    chown -R root:$APP_USER $CONFIG_DIR
    
    # Set permissions
    chmod 755 $APP_DIR
    chmod 750 $CONFIG_DIR
    chmod 755 $LOG_DIR
    
    print_success "Directories created and configured"
}

# Install binary
install_binary() {
    print_status "Installing go-abm-idp binary"
    
    if [[ ! -f "bin/go-abm-idp-linux-amd64" ]]; then
        print_error "Binary file bin/go-abm-idp-linux-amd64 not found!"
        print_error "Please run the build process first"
        exit 1
    fi
    
    # Copy binary
    cp bin/go-abm-idp-linux-amd64 $APP_DIR/bin/go-abm-idp
    chmod +x $APP_DIR/bin/go-abm-idp
    chown $APP_USER:$APP_USER $APP_DIR/bin/go-abm-idp
    
    print_success "Binary installed to $APP_DIR/bin/go-abm-idp"
}

# Install configuration
install_config() {
    print_status "Installing configuration files"
    
    # Copy configuration files
    if [[ -d "config" ]]; then
        cp -r config/* $CONFIG_DIR/
        chown -R root:$APP_USER $CONFIG_DIR
        chmod -R 640 $CONFIG_DIR/*.yml
        print_success "Configuration files installed"
    else
        print_warning "No config directory found, creating default config"
        
        # Create default configuration
        cat > $CONFIG_DIR/app.production.yml << 'EOF'
app:
  name: "go-abm-idp"
  version: "1.0.0"
  env: "production"
  debug: false

http:
  host: "0.0.0.0"
  port: 8000

database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  database: "go_abm_idp"
  username: "abm_idp"
  password: "your_password_here"

jwt:
  secret: "your_jwt_secret_here"
  expire: 3600

log:
  level: "info"
  format: "json"
  output: "/var/log/go-abm-idp/app.log"
EOF
        
        chown root:$APP_USER $CONFIG_DIR/app.production.yml
        chmod 640 $CONFIG_DIR/app.production.yml
        print_success "Default configuration created"
    fi
}

# Create systemd service
create_service() {
    print_status "Creating systemd service"
    
    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Apple Business Manager Identity Provider
Documentation=https://github.com/your-org/go-abm-idp
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/bin/go-abm-idp
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR $LOG_DIR
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Environment
Environment=GIN_MODE=release
Environment=CONFIG_PATH=$CONFIG_DIR/app.production.yml

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd
    systemctl daemon-reload
    print_success "Systemd service created"
}

# Install SSL certificates (if provided)
install_ssl() {
    print_status "Checking for SSL certificates"
    
    if [[ -f "server.crt" && -f "server.key" ]]; then
        mkdir -p $APP_DIR/ssl
        cp server.crt $APP_DIR/ssl/
        cp server.key $APP_DIR/ssl/
        chown -R $APP_USER:$APP_USER $APP_DIR/ssl
        chmod 600 $APP_DIR/ssl/server.key
        chmod 644 $APP_DIR/ssl/server.crt
        print_success "SSL certificates installed"
    else
        print_warning "No SSL certificates found (server.crt, server.key)"
        print_warning "You may need to configure SSL certificates manually"
    fi
}

# Setup firewall (if ufw is available)
setup_firewall() {
    if command -v ufw &> /dev/null; then
        print_status "Configuring firewall"
        ufw allow 8000/tcp comment "go-abm-idp HTTP"
        ufw allow 8443/tcp comment "go-abm-idp HTTPS"
        print_success "Firewall rules added"
    else
        print_warning "UFW not found, please configure firewall manually"
        print_warning "Allow ports: 8000 (HTTP), 8443 (HTTPS)"
    fi
}

# Main installation function
main() {
    print_status "Starting go-abm-idp installation"
    
    check_root
    create_user
    create_directories
    install_binary
    install_config
    create_service
    install_ssl
    setup_firewall
    
    print_success "Installation completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Edit configuration: $CONFIG_DIR/app.production.yml"
    echo "2. Configure database connection"
    echo "3. Set up SSL certificates (if not done)"
    echo "4. Start the service: systemctl start $SERVICE_NAME"
    echo "5. Enable auto-start: systemctl enable $SERVICE_NAME"
    echo "6. Check status: systemctl status $SERVICE_NAME"
    echo "7. View logs: journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "🌐 Service will be available at:"
    echo "   HTTP:  http://your-server:8000"
    echo "   HTTPS: https://your-server:8443"
    echo ""
    echo "🍎 Apple Business Manager OIDC Discovery:"
    echo "   https://your-server:8443/.well-known/openid_configuration"
}

# Run main function
main "$@"
